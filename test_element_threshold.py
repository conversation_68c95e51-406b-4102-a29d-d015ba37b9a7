#!/usr/bin/env python3
"""
测试元素流向分析的阈值问题
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_element_threshold_issue():
    """测试元素流向分析的阈值问题"""
    print("=" * 80)
    print("测试元素流向分析阈值问题")
    print("=" * 80)
    
    try:
        # 导入优化版本
        from src.optimized import compatibility_wrapper as opt_sl
        
        # 使用GPS测试文件
        gas_file = "TEST/test_gps.out"
        chemkin_file = "TEST/test_gps_results.xlsm"
        
        print("1. 加载数据...")
        
        # 创建对象
        gas_obj = opt_sl.solution(gas_file, test=True)
        gas_obj.process_gas_out(False)  # 不显示进度条
        
        chemkin_obj = opt_sl.ckfile(chemkin_file)
        chemkin_obj.load_chemkin_file()
        chemkin_obj.combine_sheets(gas_obj, progress_bar=False)
        
        print("2. 测试元素流向分析...")
        
        # 测试参数
        test_element = 'C'
        test_reactants = ['CH4']
        
        print(f"   使用元素: {test_element}")
        print(f"   使用反应物: {test_reactants}")
        
        # 初始化元素分析
        gas_obj.ele_ini(chemkin_obj, test_reactants)
        
        print("3. 测试不同阈值...")
        
        thresholds = [0.1, 0.05, 0.01, 0.005, 0.001, 0.0001, 0.0]
        
        for threshold in thresholds:
            print(f"\n   测试阈值: {threshold}")
            
            # 调用element_plot
            fig = gas_obj.element_plot(chemkin_obj, test_element, threshold=threshold)
            
            # 检查结果
            if hasattr(gas_obj, 'element_percent'):
                element_percent = gas_obj.element_percent
                print(f"     过滤后shape: {element_percent.shape}")
                
                if element_percent.shape[1] > 0:
                    print(f"     列数: {element_percent.shape[1]}")
                    print(f"     最大值: {element_percent.abs().max().max():.6f}")
                    print(f"     列名: {list(element_percent.columns[:5])}...")
                else:
                    print("     ❌ 没有数据列")
            
            if hasattr(gas_obj, 'element_percent_display'):
                display_data = gas_obj.element_percent_display
                print(f"     显示数据shape: {display_data.shape}")
                
                if display_data.shape[0] > 0:
                    print(f"     ✅ 有 {display_data.shape[0]} 行显示数据")
                    break
                else:
                    print("     ❌ 显示数据为空")
        
        print("\n4. 检查原始计算数据...")
        
        # 重新计算，不过滤
        gas_obj.element_plot(chemkin_obj, test_element, threshold=0.0)
        
        if hasattr(gas_obj, 'element_percent'):
            element_percent_raw = gas_obj.element_percent
            print(f"   原始数据shape: {element_percent_raw.shape}")
            
            if element_percent_raw.shape[1] > 0:
                # 计算每列的最大值
                max_values = element_percent_raw.abs().max()
                print(f"   各列最大值范围: {max_values.min():.6f} - {max_values.max():.6f}")
                print(f"   大于0.005的列数: {sum(max_values > 0.005)}")
                print(f"   大于0.001的列数: {sum(max_values > 0.001)}")
                print(f"   大于0.0001的列数: {sum(max_values > 0.0001)}")
                
                # 显示前几个最大值
                top_species = max_values.nlargest(10)
                print(f"   前10个组分的最大值:")
                for species, value in top_species.items():
                    print(f"     {species}: {value:.6f}")
        
        print("\n✅ 阈值测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 阈值测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始元素流向分析阈值测试")
    
    success = test_element_threshold_issue()
    
    if success:
        print("\n🎉 测试完成！")
        print("\n建议:")
        print("1. 如果所有阈值都导致空结果，检查数据计算逻辑")
        print("2. 如果某个较小阈值有结果，调整默认阈值")
        print("3. 考虑添加'显示所有数据'选项")
        return True
    else:
        print("\n❌ 测试失败，需要进一步调试")
        return False


if __name__ == "__main__":
    main()
