#!/usr/bin/env python3
"""
测试GPS页面修复功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_tab_navigation_fix():
    """测试标签页导航修复"""
    print("=" * 80)
    print("测试标签页导航修复")
    print("=" * 80)
    
    try:
        # 读取GPS页面文件
        gps_page_path = "pages/8GPS全局路径分析.py"
        
        with open(gps_page_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("1. 检查标签页导航实现...")
        
        # 检查是否使用了radio按钮替代st.tabs
        if "st.radio(" in content and "tab_selector" in content:
            print("   ✅ 找到radio按钮标签页选择器")
        else:
            print("   ❌ 缺少radio按钮标签页选择器")
        
        # 检查session state标签页状态管理
        if "active_tab" in content and "st.session_state.active_tab" in content:
            print("   ✅ 找到标签页状态管理")
        else:
            print("   ❌ 缺少标签页状态管理")
        
        # 检查条件显示替代with tab语法
        if "if selected_tab == 0:" in content and "elif selected_tab == 1:" in content:
            print("   ✅ 找到条件显示标签页内容")
        else:
            print("   ❌ 缺少条件显示标签页内容")
        
        # 检查是否移除了st.tabs
        if "st.tabs([" not in content:
            print("   ✅ 已移除st.tabs调用")
        else:
            print("   ❌ 仍有st.tabs调用")
        
        print("✅ 标签页导航修复测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 标签页导航修复测试失败: {e}")
        return False


def test_analysis_summary_removal():
    """测试分析结果摘要移除"""
    print("\n" + "=" * 80)
    print("测试分析结果摘要移除")
    print("=" * 80)
    
    try:
        # 读取GPS页面文件
        gps_page_path = "pages/8GPS全局路径分析.py"
        
        with open(gps_page_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("1. 检查分析结果摘要是否被移除...")
        
        # 检查摘要相关内容
        summary_indicators = [
            "分析结果摘要",
            "全局路径数量",
            "枢纽组分数量", 
            "选择组分数量",
            "st.metric("
        ]
        
        found_summary = False
        for indicator in summary_indicators:
            if indicator in content:
                print(f"   ❌ 仍有摘要内容: {indicator}")
                found_summary = True
        
        if not found_summary:
            print("   ✅ 分析结果摘要已完全移除")
        
        print("✅ 分析结果摘要移除测试完成")
        return not found_summary
        
    except Exception as e:
        print(f"❌ 分析结果摘要移除测试失败: {e}")
        return False


def test_download_button_contrast():
    """测试下载按钮对比度修复"""
    print("\n" + "=" * 80)
    print("测试下载按钮对比度修复")
    print("=" * 80)
    
    try:
        # 读取主题管理器文件
        theme_path = "src/theme_manager.py"
        
        with open(theme_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("1. 检查下载按钮样式修复...")
        
        # 检查下载按钮样式
        download_button_fixes = [
            ".stDownloadButton > button",
            "color: white !important",
            "强制所有按钮文字为白色",
            "button[kind=\"primary\"]",
            ".stDownloadButton button"
        ]
        
        for fix in download_button_fixes:
            if fix in content:
                print(f"   ✅ 找到下载按钮修复: {fix}")
            else:
                print(f"   ❌ 缺少下载按钮修复: {fix}")
        
        # 检查亮色和暗色主题都有修复
        light_theme_fixes = content.count(".stDownloadButton > button")
        if light_theme_fixes >= 2:  # 亮色和暗色主题
            print("   ✅ 亮色和暗色主题都有下载按钮修复")
        else:
            print("   ❌ 下载按钮修复不完整")
        
        print("✅ 下载按钮对比度修复测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 下载按钮对比度修复测试失败: {e}")
        return False


def test_element_flow_compatibility():
    """测试元素流向分析兼容性"""
    print("\n" + "=" * 80)
    print("测试元素流向分析兼容性")
    print("=" * 80)
    
    try:
        from solution0606 import solution as original_solution, ckfile as original_ckfile
        
        # 使用GPS测试文件
        gas_file = "TEST/test_gps.out"
        chemkin_file = "TEST/test_gps_results.xlsm"
        
        print("1. 快速验证元素流向分析...")
        
        # 加载gas数据
        gas_obj = original_solution(gas_file, test=True)
        gas_obj.process_gas_out()
        
        # 检查关键属性
        if hasattr(gas_obj, 'elements') and gas_obj.elements is not None:
            print(f"   ✅ elements属性正常: {len(gas_obj.elements)} 个元素")
        else:
            print("   ❌ elements属性异常")
            return False
        
        if hasattr(gas_obj, 'species_element') and gas_obj.species_element is not None:
            print(f"   ✅ species_element属性正常: {gas_obj.species_element.shape}")
        else:
            print("   ❌ species_element属性异常")
            return False
        
        print("✅ 元素流向分析兼容性测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 元素流向分析兼容性测试失败: {e}")
        return False


def test_file_syntax():
    """测试文件语法"""
    print("\n" + "=" * 80)
    print("测试文件语法")
    print("=" * 80)
    
    try:
        # 检查GPS页面语法
        print("检查GPS页面语法...")
        with open("pages/8GPS全局路径分析.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        # 简单的语法检查
        compile(code, "pages/8GPS全局路径分析.py", 'exec')
        print("   ✅ GPS页面语法正确")
        
        # 检查主题管理器语法
        print("检查主题管理器语法...")
        with open("src/theme_manager.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        compile(code, "src/theme_manager.py", 'exec')
        print("   ✅ 主题管理器语法正确")
        
        print("✅ 文件语法测试完成")
        return True
        
    except SyntaxError as e:
        print(f"   ❌ 语法错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 文件语法测试失败: {e}")
        return False


def test_gps_functionality():
    """测试GPS功能完整性"""
    print("\n" + "=" * 80)
    print("测试GPS功能完整性")
    print("=" * 80)
    
    try:
        # 读取GPS页面文件
        gps_page_path = "pages/8GPS全局路径分析.py"
        
        with open(gps_page_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("1. 检查GPS核心功能...")
        
        # 检查关键功能
        key_functions = [
            "run_gps_analysis_auto",
            "路径详情区域",
            "GPSA参数比较",
            "参数趋势分析",
            "组分选择分析",
            "download_button"
        ]
        
        for func in key_functions:
            if func in content:
                print(f"   ✅ 找到功能: {func}")
            else:
                print(f"   ❌ 缺少功能: {func}")
        
        # 检查自动分析功能
        if "auto_run_analysis" in content and "required_params_available" in content:
            print("   ✅ 自动分析功能完整")
        else:
            print("   ❌ 自动分析功能不完整")
        
        # 检查进度指示器
        if "st.progress(" in content and "status_text" in content:
            print("   ✅ 进度指示器功能完整")
        else:
            print("   ❌ 进度指示器功能不完整")
        
        print("✅ GPS功能完整性测试完成")
        return True
        
    except Exception as e:
        print(f"❌ GPS功能完整性测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始GPS页面修复测试")
    
    results = []
    
    # 运行各项测试
    results.append(test_tab_navigation_fix())
    results.append(test_analysis_summary_removal())
    results.append(test_download_button_contrast())
    results.append(test_element_flow_compatibility())
    results.append(test_file_syntax())
    results.append(test_gps_functionality())
    
    # 总结结果
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！GPS页面修复成功完成")
        print("\n主要修复:")
        print("✅ 修复了标签页导航跳转问题")
        print("✅ 移除了冗余的分析结果摘要")
        print("✅ 修复了下载按钮字体对比度问题")
        print("✅ 确认元素流向分析功能正常")
        print("✅ 保持了所有GPS分析功能")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ GPS页面修复已准备就绪!")
    else:
        print("\n❌ 发现问题，需要进一步调试")
