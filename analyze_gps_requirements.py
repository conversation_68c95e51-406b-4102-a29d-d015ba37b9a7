#!/usr/bin/env python3
"""
分析GPS需求和数据格式
"""

import pandas as pd
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from solution0606 import solution as original_solution, ckfile as original_ckfile


def analyze_gps_test_files():
    """分析GPS测试文件的结构"""
    print("=" * 80)
    print("GPS测试文件分析")
    print("=" * 80)
    
    # 检查GPS Excel文件
    gps_excel = "TEST/test_gps_results.xlsm"
    
    try:
        print(f"\n1. 分析GPS Excel文件: {gps_excel}")
        xlsx = pd.ExcelFile(gps_excel)
        print(f"   工作表: {xlsx.sheet_names}")
        
        # 检查每个工作表的内容
        for sheet_name in xlsx.sheet_names:
            try:
                sheet_data = xlsx.parse(sheet_name, index_col=0)
                print(f"\n   工作表 '{sheet_name}':")
                print(f"     形状: {sheet_data.shape}")
                print(f"     列数: {len(sheet_data.columns)}")
                
                # 分析列类型
                net_cols = [col for col in sheet_data.columns if 'Net' in col]
                temp_cols = [col for col in sheet_data.columns if 'Temperature' in col]
                pressure_cols = [col for col in sheet_data.columns if 'Pressure' in col]
                mole_cols = [col for col in sheet_data.columns if 'Mole_fraction' in col]
                
                print(f"     Net列: {len(net_cols)} 个")
                print(f"     Temperature列: {len(temp_cols)} 个")
                print(f"     Pressure列: {len(pressure_cols)} 个")
                print(f"     Mole_fraction列: {len(mole_cols)} 个")
                
                if net_cols:
                    print(f"     前3个Net列: {net_cols[:3]}")
                if mole_cols:
                    print(f"     前3个Mole_fraction列: {mole_cols[:3]}")
                    
            except Exception as e:
                print(f"     ❌ 无法读取工作表 '{sheet_name}': {e}")
    
    except Exception as e:
        print(f"❌ 无法读取GPS Excel文件: {e}")
    
    # 测试GPS gas.out文件
    print(f"\n2. 测试GPS gas.out文件处理")
    gas_file = "TEST/test_gps.out"
    
    try:
        gas_obj = original_solution(gas_file, test=True)
        gas_obj.process_gas_out()
        
        print(f"   ✅ GPS gas.out文件处理成功")
        print(f"   组分数量: {len(gas_obj.species)}")
        print(f"   反应数量: {len(gas_obj.reaction_index)}")
        print(f"   前5个组分: {gas_obj.species[:5]}")
        
        # 检查元素组成
        if hasattr(gas_obj, 'element_list'):
            print(f"   元素列表: {gas_obj.element_list}")
        
        # 检查化学计量系数
        if hasattr(gas_obj, 'stoichimetric'):
            print(f"   化学计量系数矩阵形状: {gas_obj.stoichimetric.shape}")
            
    except Exception as e:
        print(f"   ❌ GPS gas.out文件处理失败: {e}")
        import traceback
        traceback.print_exc()


def analyze_current_project_data():
    """分析当前项目的数据格式"""
    print("\n" + "=" * 80)
    print("当前项目数据格式分析")
    print("=" * 80)
    
    # 使用GPS测试文件
    gas_file = "TEST/test_gps.out"
    chemkin_file = "TEST/test_gps_results.xlsm"
    
    try:
        print("1. 加载GPS测试数据...")
        
        # 加载gas数据
        gas_obj = original_solution(gas_file, test=True)
        gas_obj.process_gas_out()
        
        # 加载chemkin数据
        chemkin_obj = original_ckfile(chemkin_file)
        chemkin_obj.load_chemkin_file()
        chemkin_obj.combine_sheets(gas_obj, progress_bar=False)
        
        print("2. 分析数据结构...")
        
        # 检查关键属性
        key_attrs = ['species', 'reaction_index', 'stoichimetric', 'element_list', 
                    'integral_ROP', 'end_ROP', 'soln_collect', 'mole_fractions']
        
        for attr in key_attrs:
            if hasattr(gas_obj, attr):
                val = getattr(gas_obj, attr)
                if isinstance(val, dict):
                    print(f"   ✅ {attr}: 字典，{len(val)} 个键")
                    if val:
                        first_key = list(val.keys())[0]
                        first_val = val[first_key]
                        if hasattr(first_val, 'shape'):
                            print(f"      第一个值形状: {first_val.shape}")
                elif hasattr(val, 'shape'):
                    print(f"   ✅ {attr}: DataFrame/数组，形状: {val.shape}")
                elif isinstance(val, list):
                    print(f"   ✅ {attr}: 列表，{len(val)} 个元素")
                    if val:
                        print(f"      前5个: {val[:5]}")
                else:
                    print(f"   ✅ {attr}: {type(val)}")
            else:
                print(f"   ❌ {attr}: 不存在")
        
        print("\n3. GPS所需数据检查...")
        
        # 检查GPS所需的关键数据
        gps_requirements = {
            'species_names': 'species',
            'species_elemental_composition': 'element_list + species',
            'reaction_stoichiometry': 'stoichimetric',
            'net_reaction_rates': 'integral_ROP 或 end_ROP',
            'species_concentrations': 'mole_fractions',
            'reaction_enthalpies': '需要额外获取',
            'fuel_composition': '需要用户指定'
        }
        
        for gps_req, project_source in gps_requirements.items():
            print(f"   {gps_req}: {project_source}")
        
        return gas_obj, chemkin_obj
        
    except Exception as e:
        print(f"❌ 数据分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None


def check_gps_data_compatibility(gas_obj):
    """检查GPS数据兼容性"""
    print("\n" + "=" * 80)
    print("GPS数据兼容性检查")
    print("=" * 80)
    
    if not gas_obj:
        print("❌ 无法进行兼容性检查，gas对象为空")
        return
    
    missing_data = []
    available_data = []
    
    # 检查必需数据
    required_checks = [
        ('species', '组分名称列表'),
        ('element_list', '元素列表'),
        ('stoichimetric', '化学计量系数矩阵'),
        ('integral_ROP', '积分反应速率'),
        ('mole_fractions', '摩尔分数')
    ]
    
    for attr, desc in required_checks:
        if hasattr(gas_obj, attr) and getattr(gas_obj, attr) is not None:
            val = getattr(gas_obj, attr)
            if isinstance(val, (list, dict)) and len(val) > 0:
                available_data.append(f"✅ {desc} ({attr})")
            elif hasattr(val, 'shape') and val.shape[0] > 0:
                available_data.append(f"✅ {desc} ({attr})")
            else:
                missing_data.append(f"❌ {desc} ({attr}) - 数据为空")
        else:
            missing_data.append(f"❌ {desc} ({attr}) - 属性不存在")
    
    print("可用数据:")
    for item in available_data:
        print(f"  {item}")
    
    if missing_data:
        print("\n缺失数据:")
        for item in missing_data:
            print(f"  {item}")
    else:
        print("\n🎉 所有必需数据都可用!")
    
    return len(missing_data) == 0


if __name__ == "__main__":
    # 分析GPS测试文件
    analyze_gps_test_files()
    
    # 分析当前项目数据
    gas_obj, chemkin_obj = analyze_current_project_data()
    
    # 检查兼容性
    is_compatible = check_gps_data_compatibility(gas_obj)
    
    print("\n" + "=" * 80)
    print("分析总结")
    print("=" * 80)
    if is_compatible:
        print("✅ 当前项目数据与GPS需求兼容")
        print("可以开始实施GPS集成")
    else:
        print("⚠️ 存在数据兼容性问题")
        print("需要解决缺失数据问题")
