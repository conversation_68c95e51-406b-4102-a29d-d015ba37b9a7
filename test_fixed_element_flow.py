#!/usr/bin/env python3
"""
测试修复后的元素流向分析
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_fixed_element_flow():
    """测试修复后的元素流向分析"""
    print("=" * 80)
    print("测试修复后的元素流向分析")
    print("=" * 80)
    
    try:
        # 导入优化版本
        from src.optimized import compatibility_wrapper as opt_sl
        
        # 使用GPS测试文件
        gas_file = "TEST/test_gps.out"
        chemkin_file = "TEST/test_gps_results.xlsm"
        
        print("1. 加载数据...")
        
        # 创建对象
        gas_obj = opt_sl.solution(gas_file, test=True)
        gas_obj.process_gas_out(False)  # 不显示进度条
        
        chemkin_obj = opt_sl.ckfile(chemkin_file)
        chemkin_obj.load_chemkin_file()
        chemkin_obj.combine_sheets(gas_obj, progress_bar=False)
        
        print("2. 测试修复后的ele_ini...")
        
        # 测试参数
        test_element = 'C'
        test_reactants = ['CH4']
        
        print(f"   使用元素: {test_element}")
        print(f"   使用反应物: {test_reactants}")
        
        # 调用修复后的ele_ini
        gas_obj.ele_ini(chemkin_obj, test_reactants)
        
        # 检查结果
        if hasattr(gas_obj, 'elements_initial'):
            elements_initial = gas_obj.elements_initial
            print(f"   ✅ elements_initial shape: {elements_initial.shape}")
            
            if test_element in elements_initial.columns:
                element_values = elements_initial.loc[:, test_element]
                print(f"   ✅ {test_element} 元素值: {element_values.tolist()}")
                print(f"   ✅ 是否有NaN: {element_values.isna().any()}")
                print(f"   ✅ 是否全为零: {(element_values == 0).all()}")
                
                if not element_values.isna().any() and not (element_values == 0).all():
                    print("   🎉 ele_ini修复成功！")
                else:
                    print("   ❌ ele_ini仍有问题")
                    return False
            else:
                print(f"   ❌ {test_element} 不在 elements_initial.columns 中")
                return False
        else:
            print("   ❌ elements_initial 不存在")
            return False
        
        print("\n3. 测试修复后的element_plot...")
        
        # 测试不同阈值
        thresholds = [0.1, 0.01, 0.001, 0.0]
        
        for threshold in thresholds:
            print(f"\n   测试阈值: {threshold}")
            
            # 调用element_plot
            fig = gas_obj.element_plot(chemkin_obj, test_element, threshold=threshold)
            
            # 检查结果
            if hasattr(gas_obj, 'element_percent_display'):
                display_data = gas_obj.element_percent_display
                print(f"     显示数据shape: {display_data.shape}")
                
                if display_data.shape[0] > 0:
                    print(f"     ✅ 有 {display_data.shape[0]} 行显示数据")
                    print(f"     前几个组分: {list(display_data.index[:5])}")
                    print("     🎉 element_plot修复成功！")
                    break
                else:
                    print("     ❌ 显示数据为空")
            else:
                print("     ❌ element_percent_display 不存在")
        else:
            print("   ❌ 所有阈值都没有数据")
            return False
        
        print("\n4. 测试完整的用户场景...")
        
        # 模拟用户在页面上的操作
        test_scenarios = [
            {'element': 'C', 'reactants': ['CH4'], 'threshold': 0.001},
            {'element': 'H', 'reactants': ['CH4', 'H2O'], 'threshold': 0.01},
            {'element': 'O', 'reactants': ['O2', 'H2O'], 'threshold': 0.005},
            {'element': 'N', 'reactants': ['N2'], 'threshold': 0.0}  # 显示所有数据
        ]
        
        success_count = 0
        
        for i, scenario in enumerate(test_scenarios):
            print(f"\n   场景 {i+1}: 元素={scenario['element']}, 反应物={scenario['reactants']}, 阈值={scenario['threshold']}")
            
            try:
                # 重新创建对象以确保干净的状态
                gas_obj_test = opt_sl.solution(gas_file, test=True)
                gas_obj_test.process_gas_out(False)
                
                chemkin_obj_test = opt_sl.ckfile(chemkin_file)
                chemkin_obj_test.load_chemkin_file()
                chemkin_obj_test.combine_sheets(gas_obj_test, progress_bar=False)
                
                # 执行分析
                gas_obj_test.ele_ini(chemkin_obj_test, scenario['reactants'])
                fig = gas_obj_test.element_plot(chemkin_obj_test, scenario['element'], threshold=scenario['threshold'])
                
                # 检查结果
                if hasattr(gas_obj_test, 'element_percent_display'):
                    display_data = gas_obj_test.element_percent_display
                    if display_data.shape[0] > 0:
                        print(f"     ✅ 成功: {display_data.shape[0]} 行数据")
                        success_count += 1
                    else:
                        print(f"     ❌ 失败: 无数据")
                else:
                    print(f"     ❌ 失败: 无element_percent_display")
                    
            except Exception as e:
                print(f"     ❌ 异常: {e}")
        
        print(f"\n   用户场景测试: {success_count}/{len(test_scenarios)} 通过")
        
        if success_count == len(test_scenarios):
            print("   🎉 所有用户场景测试通过！")
            return True
        else:
            print("   ❌ 部分用户场景失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🚀 开始测试修复后的元素流向分析")
    
    success = test_fixed_element_flow()
    
    if success:
        print("\n🎉 修复验证成功！")
        print("\n✅ 修复总结:")
        print("1. 修复了ele_ini中的索引对齐问题")
        print("2. 使用reindex和fill_value避免NaN值")
        print("3. 确保DataFrame形状完全匹配")
        print("4. 元素流向分析现在能正常显示数据和图表")
        print("\n🚀 请重新运行元素流向分析页面，现在应该能看到数据了！")
        return True
    else:
        print("\n❌ 修复验证失败，需要进一步调试")
        return False


if __name__ == "__main__":
    main()
