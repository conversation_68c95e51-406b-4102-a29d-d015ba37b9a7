#!/usr/bin/env python3
"""
调试main_modern.py的数据流问题
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_main_modern_data_flow():
    """测试main_modern.py的数据流"""
    print("=" * 80)
    print("调试main_modern.py数据流")
    print("=" * 80)
    
    try:
        # 导入main_modern.py使用的模块
        from src.optimized import compatibility_wrapper as opt_sl
        
        print("1. 测试compatibility_wrapper导入...")
        print(f"   opt_sl module: {opt_sl}")
        print(f"   opt_sl.solution: {opt_sl.solution}")
        print(f"   opt_sl.ckfile: {opt_sl.ckfile}")
        
        # 使用GPS测试文件
        gas_file = "TEST/test_gps.out"
        chemkin_file = "TEST/test_gps_results.xlsm"
        
        print("2. 测试gas solution创建...")
        
        # 创建gas对象（模拟main_modern.py的前处理）
        gas_obj = opt_sl.solution(gas_file, test=True)
        print(f"   gas_obj type: {type(gas_obj)}")
        print(f"   gas_obj module: {gas_obj.__class__.__module__}")
        
        print("3. 测试process_gas_out...")
        gas_obj.process_gas_out(True)
        
        # 检查关键属性
        print("4. 检查gas_obj关键属性...")
        key_attrs = ['elements', 'possible_reactants', 'species', 'species_element', 'species_MW']
        for attr in key_attrs:
            if hasattr(gas_obj, attr):
                value = getattr(gas_obj, attr)
                if value is not None:
                    if hasattr(value, 'shape'):
                        print(f"   ✅ {attr}: {type(value)} with shape {value.shape}")
                    elif hasattr(value, '__len__'):
                        print(f"   ✅ {attr}: {type(value)} with length {len(value)}")
                        if isinstance(value, list) and len(value) > 0:
                            print(f"      first few items: {value[:5]}")
                    else:
                        print(f"   ✅ {attr}: {type(value)} = {value}")
                else:
                    print(f"   ❌ {attr}: None")
            else:
                print(f"   ❌ {attr}: NOT FOUND")
        
        print("5. 测试chemkin solution创建...")
        
        # 创建chemkin对象（模拟main_modern.py的后处理）
        chemkin_obj = opt_sl.ckfile(chemkin_file)
        print(f"   chemkin_obj type: {type(chemkin_obj)}")
        print(f"   chemkin_obj module: {chemkin_obj.__class__.__module__}")
        
        print("6. 测试load_chemkin_file...")
        chemkin_obj.load_chemkin_file()
        
        print("7. 测试combine_sheets...")
        chemkin_obj.combine_sheets(gas_obj, progress_bar=False)
        
        # 再次检查gas_obj的属性（combine_sheets后）
        print("8. 检查combine_sheets后的gas_obj属性...")
        for attr in key_attrs:
            if hasattr(gas_obj, attr):
                value = getattr(gas_obj, attr)
                if value is not None:
                    if hasattr(value, 'shape'):
                        print(f"   ✅ {attr}: {type(value)} with shape {value.shape}")
                    elif hasattr(value, '__len__'):
                        print(f"   ✅ {attr}: {type(value)} with length {len(value)}")
                        if isinstance(value, list) and len(value) > 0:
                            print(f"      first few items: {value[:5]}")
                    else:
                        print(f"   ✅ {attr}: {type(value)} = {value}")
                else:
                    print(f"   ❌ {attr}: None")
            else:
                print(f"   ❌ {attr}: NOT FOUND")
        
        # 检查chemkin_obj的关键属性
        print("9. 检查chemkin_obj关键属性...")
        chemkin_attrs = ['mole_fraction_exist', 'variables', 'end_point_sheets']
        for attr in chemkin_attrs:
            if hasattr(chemkin_obj, attr):
                value = getattr(chemkin_obj, attr)
                print(f"   ✅ {attr}: {value}")
            else:
                print(f"   ❌ {attr}: NOT FOUND")
        
        print("10. 测试元素流向分析功能...")
        
        # 测试ele_ini方法
        if hasattr(gas_obj, 'possible_reactants') and gas_obj.possible_reactants:
            test_reactants = gas_obj.possible_reactants[:3]  # 前3个反应物
            print(f"   使用反应物: {test_reactants}")
            gas_obj.ele_ini(chemkin_obj, test_reactants)
            
            if hasattr(gas_obj, 'elements_initial'):
                print(f"   ✅ ele_ini成功: {gas_obj.elements_initial.shape}")
            else:
                print("   ❌ ele_ini失败: elements_initial未创建")
        else:
            print("   ❌ 无法测试ele_ini: possible_reactants不可用")
        
        # 测试element_plot方法
        if hasattr(gas_obj, 'elements') and gas_obj.elements:
            test_element = gas_obj.elements[0]  # 第一个元素
            print(f"   使用元素: {test_element}")
            fig = gas_obj.element_plot(chemkin_obj, test_element, threshold=0.05)
            
            if fig is not None:
                print(f"   ✅ element_plot成功 (元素: {test_element})")
            else:
                print(f"   ❌ element_plot失败 (元素: {test_element})")
        else:
            print("   ❌ 无法测试element_plot: elements不可用")
        
        print("✅ main_modern.py数据流测试完成")
        return True
        
    except Exception as e:
        print(f"❌ main_modern.py数据流测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_main_modern_imports():
    """检查main_modern.py的导入"""
    print("\n" + "=" * 80)
    print("检查main_modern.py导入")
    print("=" * 80)
    
    try:
        # 检查main_modern.py文件
        main_modern_path = "main_modern.py"
        
        with open(main_modern_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("1. 检查导入语句...")
        
        # 检查关键导入
        imports_to_check = [
            "from src.optimized import compatibility_wrapper as opt_sl",
            "compatibility_wrapper",
            "opt_sl"
        ]
        
        for import_stmt in imports_to_check:
            if import_stmt in content:
                print(f"   ✅ 找到: {import_stmt}")
            else:
                print(f"   ❌ 未找到: {import_stmt}")
        
        # 检查solution和ckfile的使用
        usage_patterns = [
            "opt_sl.solution",
            "opt_sl.ckfile",
            "st.session_state['a']",
            "st.session_state['b']"
        ]
        
        print("2. 检查使用模式...")
        for pattern in usage_patterns:
            count = content.count(pattern)
            print(f"   {pattern}: {count} 次使用")
        
        print("✅ main_modern.py导入检查完成")
        return True
        
    except Exception as e:
        print(f"❌ main_modern.py导入检查失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始main_modern.py调试")
    
    results = []
    
    # 运行各项测试
    results.append(check_main_modern_imports())
    results.append(test_main_modern_data_flow())
    
    # 总结结果
    print("\n" + "=" * 80)
    print("调试结果总结")
    print("=" * 80)
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 main_modern.py数据流正常")
        print("\n建议:")
        print("1. 运行元素流向分析页面查看控制台调试输出")
        print("2. 检查session_state中的数据是否正确设置")
        print("3. 确认前处理和后处理步骤是否完成")
        return True
    else:
        print("❌ 发现问题，需要进一步检查")
        return False


if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 调试完成，请运行元素流向分析页面查看详细输出!")
    else:
        print("\n❌ 发现问题，需要进一步调试")
