#!/usr/bin/env python3
"""
测试GPS页面最终改进功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_automatic_analysis():
    """测试自动分析功能"""
    print("=" * 80)
    print("测试自动分析功能")
    print("=" * 80)
    
    try:
        # 读取GPS页面文件
        gps_page_path = "pages/8GPS全局路径分析.py"
        
        with open(gps_page_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("1. 检查自动分析功能...")
        
        # 检查自动分析函数
        if "run_gps_analysis_auto" in content:
            print("   ✅ 找到自动分析函数")
        else:
            print("   ❌ 缺少自动分析函数")
        
        # 检查手动按钮是否被移除
        manual_button_count = content.count("🚀 开始GPS分析")
        if manual_button_count == 0:
            print("   ✅ 手动分析按钮已移除")
        else:
            print(f"   ❌ 仍有 {manual_button_count} 个手动分析按钮")
        
        # 检查重新分析按钮
        if "🔄 重新运行分析" in content:
            print("   ✅ 找到重新分析按钮")
        else:
            print("   ❌ 缺少重新分析按钮")
        
        # 检查自动运行逻辑
        if "auto_run_analysis" in content and "required_params_available" in content:
            print("   ✅ 找到自动运行逻辑")
        else:
            print("   ❌ 缺少自动运行逻辑")
        
        print("✅ 自动分析功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 自动分析功能测试失败: {e}")
        return False


def test_pathway_details_relocation():
    """测试路径详情重新定位"""
    print("\n" + "=" * 80)
    print("测试路径详情重新定位")
    print("=" * 80)
    
    try:
        # 读取GPS页面文件
        gps_page_path = "pages/8GPS全局路径分析.py"
        
        with open(gps_page_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("1. 检查路径详情位置...")
        
        # 查找路径详情区域的位置
        pathway_details_pos = content.find("## 🛤️ 全局路径详情")
        tabs_pos = content.find("st.tabs([")
        
        if pathway_details_pos != -1 and tabs_pos != -1:
            if pathway_details_pos < tabs_pos:
                print("   ✅ 路径详情位于标签页之前")
            else:
                print("   ❌ 路径详情位置不正确")
        else:
            print("   ❌ 未找到路径详情或标签页")
        
        # 检查路径详情内容
        pathway_content_checks = [
            "路径信息表",
            "路径代码",
            "源组分",
            "目标组分",
            "路径长度",
            "完整路径"
        ]
        
        for check in pathway_content_checks:
            if check in content:
                print(f"   ✅ 找到路径详情内容: {check}")
            else:
                print(f"   ❌ 缺少路径详情内容: {check}")
        
        print("✅ 路径详情重新定位测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 路径详情重新定位测试失败: {e}")
        return False


def test_progress_indicators():
    """测试进度指示器"""
    print("\n" + "=" * 80)
    print("测试进度指示器")
    print("=" * 80)
    
    try:
        # 读取GPS页面文件
        gps_page_path = "pages/8GPS全局路径分析.py"
        
        with open(gps_page_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("1. 检查进度指示器...")
        
        # 检查进度条
        if "st.progress(" in content:
            progress_count = content.count("st.progress(")
            print(f"   ✅ 找到 {progress_count} 个进度条")
        else:
            print("   ❌ 缺少进度条")
        
        # 检查状态文本
        if "status_text" in content:
            print("   ✅ 找到状态文本指示器")
        else:
            print("   ❌ 缺少状态文本指示器")
        
        # 检查进度更新逻辑
        progress_features = [
            "progress_bar.progress(",
            "status_text.text(",
            "正在加载项目数据",
            "正在进行GPS分析",
            "正在运行GPSA分析"
        ]
        
        for feature in progress_features:
            if feature in content:
                print(f"   ✅ 找到进度功能: {feature}")
            else:
                print(f"   ❌ 缺少进度功能: {feature}")
        
        # 检查多条件趋势分析的进度指示器
        if "正在分析条件" in content and "分析条件" in content:
            print("   ✅ 找到多条件分析进度指示器")
        else:
            print("   ❌ 缺少多条件分析进度指示器")
        
        print("✅ 进度指示器测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 进度指示器测试失败: {e}")
        return False


def test_tab_structure():
    """测试标签页结构"""
    print("\n" + "=" * 80)
    print("测试标签页结构")
    print("=" * 80)
    
    try:
        # 读取GPS页面文件
        gps_page_path = "pages/8GPS全局路径分析.py"
        
        with open(gps_page_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("1. 检查标签页数量...")
        
        # 检查标签页创建
        if "tab1, tab2, tab3 = st.tabs([" in content:
            print("   ✅ 找到3个标签页创建")
        elif "tab1, tab2, tab3, tab4 = st.tabs([" in content:
            print("   ❌ 仍有4个标签页")
        else:
            print("   ❌ 标签页创建格式不正确")
        
        # 检查标签页内容
        expected_tabs = [
            "📈 GPSA参数比较",
            "📊 参数趋势分析", 
            "🎯 组分选择分析"
        ]
        
        for tab in expected_tabs:
            if tab in content:
                print(f"   ✅ 找到标签页: {tab}")
            else:
                print(f"   ❌ 缺少标签页: {tab}")
        
        # 检查是否移除了路径详情标签页
        if "🛤️ 路径详情" in content:
            # 检查是否在标签页定义中
            tabs_definition = content[content.find("st.tabs(["):content.find("])", content.find("st.tabs(["))]
            if "🛤️ 路径详情" in tabs_definition:
                print("   ❌ 路径详情仍在标签页中")
            else:
                print("   ✅ 路径详情已从标签页中移除")
        
        # 检查Tab 4内容是否被移除
        if "with tab4:" in content:
            print("   ❌ 仍有Tab 4内容")
        else:
            print("   ✅ Tab 4内容已移除")
        
        print("✅ 标签页结构测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 标签页结构测试失败: {e}")
        return False


def test_file_syntax():
    """测试文件语法"""
    print("\n" + "=" * 80)
    print("测试文件语法")
    print("=" * 80)
    
    try:
        # 检查GPS页面语法
        print("检查GPS页面语法...")
        with open("pages/8GPS全局路径分析.py", 'r', encoding='utf-8') as f:
            code = f.read()
        
        # 简单的语法检查
        compile(code, "pages/8GPS全局路径分析.py", 'exec')
        print("   ✅ GPS页面语法正确")
        
        # 检查代码行数
        lines = code.split('\n')
        print(f"   📊 代码总行数: {len(lines)}")
        
        # 检查关键函数定义
        if "def run_gps_analysis_auto():" in code:
            print("   ✅ 自动分析函数定义正确")
        else:
            print("   ❌ 自动分析函数定义有问题")
        
        print("✅ 文件语法测试完成")
        return True
        
    except SyntaxError as e:
        print(f"   ❌ GPS页面语法错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 文件语法测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始GPS页面最终改进测试")
    
    results = []
    
    # 运行各项测试
    results.append(test_automatic_analysis())
    results.append(test_pathway_details_relocation())
    results.append(test_progress_indicators())
    results.append(test_tab_structure())
    results.append(test_file_syntax())
    
    # 总结结果
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！GPS页面最终改进成功完成")
        print("\n主要改进:")
        print("✅ 实现了自动分析加载功能")
        print("✅ 将路径详情移到标签页外，始终可见")
        print("✅ 添加了详细的进度指示器")
        print("✅ 重新组织为3个标签页结构")
        print("✅ 保持了所有现有功能")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ GPS页面最终改进已准备就绪!")
    else:
        print("\n❌ 发现问题，需要进一步调试")
