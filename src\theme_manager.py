"""
主题管理器 - 支持亮色和暗色主题切换
"""
import streamlit as st

def theme_selector():
    """在侧边栏显示主题选择器"""
    with st.sidebar:
        st.markdown("---")
        
        # 确保session_state中有主题设置
        if 'current_theme' not in st.session_state:
            st.session_state.current_theme = "亮色主题"
        
        # 根据当前主题设置选择框的默认值
        current_index = 0 if st.session_state.current_theme == "亮色主题" else 1
        
        theme = st.selectbox(
            "🎨 选择主题",
            options=["亮色主题", "暗色主题"],
            index=current_index,
            key="theme_selector"
        )
        
        # 只有当主题真正改变时才更新和重新运行
        if theme != st.session_state.current_theme:
            st.session_state.current_theme = theme
            st.rerun()  # 重新运行以应用新主题
        
        return theme

def get_light_theme_css():
    """获取亮色主题CSS样式"""
    return """
    <style>
        /* 亮色主题 - 根变量 */
        :root {
            --primary-color: #1f77b4;
            --secondary-color: #ff7f0e;
            --success-color: #2ca02c;
            --warning-color: #d62728;
            --info-color: #17becf;
            --background-color: #ffffff;
            --surface-color: #f8f9fa;
            --text-color: #333333;
            --text-secondary: #666666;
            --border-color: #e0e0e0;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --shadow-light: rgba(0, 0, 0, 0.05);
            --shadow-hover: rgba(0, 0, 0, 0.15);
        }
        
        /* 主体背景 */
        .stApp {
            background-color: var(--background-color);
            color: var(--text-color);
        }
        
        /* 侧边栏 */
        .css-1d391kg {
            background-color: var(--surface-color);
        }
        
        /* 现代化卡片样式 */
        .analysis-card {
            background: linear-gradient(135deg, rgba(31, 119, 180, 0.1), rgba(23, 190, 207, 0.1));
            padding: 1.5rem;
            border-radius: 15px;
            border-left: 4px solid var(--primary-color);
            box-shadow: 0 4px 6px var(--shadow-color);
            margin: 1rem 0;
            color: var(--text-color);
        }
        
        .parameter-card {
            background: var(--background-color);
            padding: 1.2rem;
            border-radius: 10px;
            box-shadow: 0 2px 4px var(--shadow-color);
            border: 1px solid var(--border-color);
            margin: 0.5rem 0;
            color: var(--text-color);
        }
        
        .error-card {
            background: linear-gradient(135deg, rgba(214, 39, 40, 0.1), rgba(255, 127, 14, 0.1));
            padding: 1.5rem;
            border-radius: 15px;
            border-left: 4px solid var(--warning-color);
            box-shadow: 0 4px 6px var(--shadow-color);
            margin: 1rem 0;
            color: var(--text-color);
        }
        
        .success-card {
            background: linear-gradient(135deg, rgba(44, 160, 44, 0.1), rgba(23, 190, 207, 0.1));
            padding: 1.5rem;
            border-radius: 15px;
            border-left: 4px solid var(--success-color);
            box-shadow: 0 4px 6px var(--shadow-color);
            margin: 1rem 0;
            color: var(--text-color);
        }

        .warning-card {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 152, 0, 0.1));
            padding: 1.5rem;
            border-radius: 15px;
            border-left: 4px solid #ffc107;
            box-shadow: 0 4px 6px var(--shadow-color);
            margin: 1rem 0;
            color: var(--text-color);
        }
        
        /* 按钮样式 */
        .stButton > button {
            background: linear-gradient(90deg, var(--primary-color), var(--info-color));
            color: white !important;
            border: none;
            border-radius: 8px;
            padding: 0.6rem 1.5rem;
            font-weight: 600;
            box-shadow: 0 2px 4px var(--shadow-color);
            transition: all 0.3s ease;
        }

        .stButton > button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-hover);
            color: white !important;
        }

        /* 下载按钮特殊样式 */
        .stDownloadButton > button {
            background: linear-gradient(90deg, var(--success-color), var(--info-color));
            color: white !important;
            border: none;
            border-radius: 8px;
            padding: 0.6rem 1.5rem;
            font-weight: 600;
            box-shadow: 0 2px 4px var(--shadow-color);
            transition: all 0.3s ease;
        }

        .stDownloadButton > button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-hover);
            color: white !important;
        }

        /* 强制所有按钮文字为白色 */
        button[kind="primary"],
        button[kind="secondary"],
        .stDownloadButton button,
        .stButton button {
            color: white !important;
        }
        
        /* 标签页样式 */
        .stTabs [data-baseweb="tab-list"] {
            gap: 8px;
        }
        
        .stTabs [data-baseweb="tab"] {
            background: var(--background-color);
            border-radius: 8px;
            padding: 0.5rem 1rem;
            border: 1px solid var(--border-color);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            color: var(--text-color);
        }
        
        .stTabs [aria-selected="true"] {
            background: linear-gradient(90deg, var(--primary-color), var(--info-color));
            color: white !important;
            border: none;
        }
        
        /* 输入控件样式 - 增强对比度 */
        .stSelectbox > div > div {
            border-radius: 8px;
            border: 1px solid var(--border-color);
            background-color: var(--background-color) !important;
        }

        /* 选择框文本颜色修复 */
        .stSelectbox > div > div > div {
            color: var(--text-color) !important;
            background-color: var(--background-color) !important;
        }

        /* 选择框下拉选项 */
        .stSelectbox > div > div > div > div {
            color: var(--text-color) !important;
            background-color: var(--background-color) !important;
        }

        /* 选择框标签 */
        .stSelectbox > label {
            color: var(--text-color) !important;
        }

        .stNumberInput > div > div > input {
            border-radius: 8px;
            border: 1px solid var(--border-color);
            background-color: var(--background-color);
            color: var(--text-color) !important;
        }

        /* 单选按钮样式 */
        .stRadio > label {
            color: var(--text-color) !important;
        }

        .stRadio > div {
            color: var(--text-color) !important;
        }

        /* 多选框样式 */
        .stMultiSelect > label {
            color: var(--text-color) !important;
        }

        .stMultiSelect > div > div {
            background-color: var(--background-color) !important;
            color: var(--text-color) !important;
        }
        
        /* 文本颜色 */
        .stMarkdown, .stText, p, h1, h2, h3, h4, h5, h6 {
            color: var(--text-color) !important;
        }
        
        /* 数据表格 */
        .stDataFrame {
            background-color: var(--background-color);
        }
        
        /* 进度条 */
        .stProgress > div > div {
            background-color: var(--primary-color);
        }
        
        /* 指标卡片 */
        .stMetric {
            background-color: var(--surface-color);
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }
        
        /* 侧边栏文字颜色修复 */
        .css-1d391kg .stMarkdown, 
        .css-1d391kg .stSelectbox label,
        .css-1d391kg .stRadio label,
        .css-1d391kg h1, .css-1d391kg h2, .css-1d391kg h3, .css-1d391kg h4, .css-1d391kg h5, .css-1d391kg h6,
        .css-1d391kg p, .css-1d391kg div {
            color: var(--text-color) !important;
        }
        
        /* 侧边栏选择框样式 */
        .css-1d391kg .stSelectbox > div > div {
            background-color: var(--background-color);
            border: 1px solid var(--border-color);
            color: var(--text-color);
        }
        
        /* 增强侧边栏文字对比度 - 使用更强的选择器 */
        section[data-testid="stSidebar"] *,
        section[data-testid="stSidebar"] .stMarkdown,
        section[data-testid="stSidebar"] h1, section[data-testid="stSidebar"] h2, section[data-testid="stSidebar"] h3,
        section[data-testid="stSidebar"] h4, section[data-testid="stSidebar"] h5, section[data-testid="stSidebar"] h6,
        section[data-testid="stSidebar"] p, section[data-testid="stSidebar"] div, section[data-testid="stSidebar"] span,
        section[data-testid="stSidebar"] label, section[data-testid="stSidebar"] .stCaption,
        .css-1d391kg span, .css-1d391kg label {
            color: var(--text-color) !important;
        }
        
        /* 侧边栏选择框选项文字 */
        section[data-testid="stSidebar"] .stSelectbox > div > div > div,
        .css-1d391kg .stSelectbox > div > div > div {
            color: var(--text-color) !important;
            background-color: var(--surface-color) !important;
        }
        
        /* 侧边栏按钮文字保持白色 */
        section[data-testid="stSidebar"] .stButton > button,
        .css-1d391kg .stButton > button {
            color: white !important;
        }
        
        /* 侧边栏背景 */
        section[data-testid="stSidebar"] {
            background-color: var(--surface-color) !important;
        }
        
        /* 侧边栏分隔线 */
        section[data-testid="stSidebar"] hr,
        .css-1d391kg hr {
            border-color: var(--border-color) !important;
        }

        /* 全局表单元素对比度修复 */
        div[data-testid="stSelectbox"] > label,
        div[data-testid="stSelectbox"] > div > div,
        div[data-testid="stSelectbox"] > div > div > div,
        div[data-testid="stSelectbox"] span {
            color: var(--text-color) !important;
            background-color: var(--background-color) !important;
        }

        /* 下拉菜单选项 */
        ul[role="listbox"] li,
        div[role="option"] {
            color: var(--text-color) !important;
            background-color: var(--background-color) !important;
        }

        /* 输入框通用样式 */
        input[type="text"], input[type="number"], textarea, select {
            color: var(--text-color) !important;
            background-color: var(--background-color) !important;
            border-color: var(--border-color) !important;
        }

        /* 标签通用样式 */
        label, .stCaption {
            color: var(--text-color) !important;
        }
    </style>
    """

def get_dark_theme_css():
    """获取暗色主题CSS样式"""
    return """
    <style>
        /* 暗色主题 - 根变量 */
        :root {
            --primary-color: #4a9eff;
            --secondary-color: #ff9f40;
            --success-color: #4caf50;
            --warning-color: #f44336;
            --info-color: #00bcd4;
            --background-color: #1e1e1e;
            --surface-color: #2d2d2d;
            --text-color: #ffffff;
            --text-secondary: #b0b0b0;
            --border-color: #404040;
            --shadow-color: rgba(0, 0, 0, 0.3);
            --shadow-light: rgba(0, 0, 0, 0.2);
            --shadow-hover: rgba(0, 0, 0, 0.4);
        }
        
        /* 主体背景 */
        .stApp {
            background-color: var(--background-color);
            color: var(--text-color);
        }
        
        /* 侧边栏 */
        .css-1d391kg {
            background-color: var(--surface-color);
        }
        
        /* 现代化卡片样式 */
        .analysis-card {
            background: linear-gradient(135deg, rgba(74, 158, 255, 0.2), rgba(0, 188, 212, 0.2));
            padding: 1.5rem;
            border-radius: 15px;
            border-left: 4px solid var(--primary-color);
            box-shadow: 0 4px 6px var(--shadow-color);
            margin: 1rem 0;
            color: var(--text-color);
        }
        
        .parameter-card {
            background: var(--surface-color);
            padding: 1.2rem;
            border-radius: 10px;
            box-shadow: 0 2px 4px var(--shadow-color);
            border: 1px solid var(--border-color);
            margin: 0.5rem 0;
            color: var(--text-color);
        }
        
        .error-card {
            background: linear-gradient(135deg, rgba(244, 67, 54, 0.2), rgba(255, 159, 64, 0.2));
            padding: 1.5rem;
            border-radius: 15px;
            border-left: 4px solid var(--warning-color);
            box-shadow: 0 4px 6px var(--shadow-color);
            margin: 1rem 0;
            color: var(--text-color);
        }
        
        .success-card {
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.2), rgba(0, 188, 212, 0.2));
            padding: 1.5rem;
            border-radius: 15px;
            border-left: 4px solid var(--success-color);
            box-shadow: 0 4px 6px var(--shadow-color);
            margin: 1rem 0;
            color: var(--text-color);
        }

        .warning-card {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 152, 0, 0.2));
            padding: 1.5rem;
            border-radius: 15px;
            border-left: 4px solid #ffc107;
            box-shadow: 0 4px 6px var(--shadow-color);
            margin: 1rem 0;
            color: var(--text-color);
        }
        
        /* 按钮样式 */
        .stButton > button {
            background: linear-gradient(90deg, var(--primary-color), var(--info-color));
            color: white !important;
            border: none;
            border-radius: 8px;
            padding: 0.6rem 1.5rem;
            font-weight: 600;
            box-shadow: 0 2px 4px var(--shadow-color);
            transition: all 0.3s ease;
        }

        .stButton > button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-hover);
            color: white !important;
        }

        /* 下载按钮特殊样式 */
        .stDownloadButton > button {
            background: linear-gradient(90deg, var(--success-color), var(--info-color));
            color: white !important;
            border: none;
            border-radius: 8px;
            padding: 0.6rem 1.5rem;
            font-weight: 600;
            box-shadow: 0 2px 4px var(--shadow-color);
            transition: all 0.3s ease;
        }

        .stDownloadButton > button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-hover);
            color: white !important;
        }

        /* 强制所有按钮文字为白色 */
        button[kind="primary"],
        button[kind="secondary"],
        .stDownloadButton button,
        .stButton button {
            color: white !important;
        }
        
        /* 标签页样式 */
        .stTabs [data-baseweb="tab-list"] {
            gap: 8px;
        }
        
        .stTabs [data-baseweb="tab"] {
            background: var(--surface-color);
            border-radius: 8px;
            padding: 0.5rem 1rem;
            border: 1px solid var(--border-color);
            box-shadow: 0 2px 4px var(--shadow-light);
            color: var(--text-color);
        }
        
        .stTabs [aria-selected="true"] {
            background: linear-gradient(90deg, var(--primary-color), var(--info-color));
            color: white !important;
            border: none;
        }
        
        /* 输入控件样式 - 增强对比度 */
        .stSelectbox > div > div {
            border-radius: 8px;
            border: 1px solid var(--border-color);
            background-color: var(--surface-color) !important;
        }

        /* 选择框文本颜色修复 */
        .stSelectbox > div > div > div {
            color: var(--text-color) !important;
            background-color: var(--surface-color) !important;
        }

        /* 选择框下拉选项 */
        .stSelectbox > div > div > div > div {
            color: var(--text-color) !important;
            background-color: var(--surface-color) !important;
        }

        /* 选择框标签 */
        .stSelectbox > label {
            color: var(--text-color) !important;
        }

        .stNumberInput > div > div > input {
            border-radius: 8px;
            border: 1px solid var(--border-color);
            background-color: var(--surface-color);
            color: var(--text-color) !important;
        }

        /* 单选按钮样式 */
        .stRadio > label {
            color: var(--text-color) !important;
        }

        .stRadio > div {
            color: var(--text-color) !important;
        }

        /* 多选框样式 */
        .stMultiSelect > label {
            color: var(--text-color) !important;
        }

        .stMultiSelect > div > div {
            background-color: var(--surface-color) !important;
            color: var(--text-color) !important;
        }
        
        /* 文本颜色 */
        .stMarkdown, .stText, p, h1, h2, h3, h4, h5, h6 {
            color: var(--text-color) !important;
        }
        
        /* 数据表格 */
        .stDataFrame {
            background-color: var(--surface-color);
        }
        
        /* 进度条 */
        .stProgress > div > div {
            background-color: var(--primary-color);
        }
        
        /* 指标卡片 */
        .stMetric {
            background-color: var(--surface-color);
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }
        
        /* 覆盖Streamlit默认的暗色样式 */
        .stApp > header {
            background-color: transparent;
        }
        
        /* 输入框文本颜色 */
        input, textarea, select {
            color: var(--text-color) !important;
        }
        
        /* 下拉菜单 */
        .stSelectbox > div > div > div {
            background-color: var(--surface-color);
            color: var(--text-color);
        }
        
        /* 侧边栏文字颜色修复 */
        .css-1d391kg .stMarkdown, 
        .css-1d391kg .stSelectbox label,
        .css-1d391kg .stRadio label,
        .css-1d391kg h1, .css-1d391kg h2, .css-1d391kg h3, .css-1d391kg h4, .css-1d391kg h5, .css-1d391kg h6,
        .css-1d391kg p, .css-1d391kg div {
            color: var(--text-color) !important;
        }
        
        /* 侧边栏选择框样式 */
        .css-1d391kg .stSelectbox > div > div {
            background-color: var(--background-color);
            border: 1px solid var(--border-color);
            color: var(--text-color);
        }
        
        /* 增强侧边栏文字对比度 - 使用更强的选择器 */
        section[data-testid="stSidebar"] *,
        section[data-testid="stSidebar"] .stMarkdown,
        section[data-testid="stSidebar"] h1, section[data-testid="stSidebar"] h2, section[data-testid="stSidebar"] h3,
        section[data-testid="stSidebar"] h4, section[data-testid="stSidebar"] h5, section[data-testid="stSidebar"] h6,
        section[data-testid="stSidebar"] p, section[data-testid="stSidebar"] div, section[data-testid="stSidebar"] span,
        section[data-testid="stSidebar"] label, section[data-testid="stSidebar"] .stCaption,
        .css-1d391kg span, .css-1d391kg label {
            color: var(--text-color) !important;
        }
        
        /* 侧边栏选择框选项文字 */
        section[data-testid="stSidebar"] .stSelectbox > div > div > div,
        .css-1d391kg .stSelectbox > div > div > div {
            color: var(--text-color) !important;
            background-color: var(--surface-color) !important;
        }
        
        /* 侧边栏按钮文字保持白色 */
        section[data-testid="stSidebar"] .stButton > button,
        .css-1d391kg .stButton > button {
            color: white !important;
        }
        
        /* 侧边栏背景 */
        section[data-testid="stSidebar"] {
            background-color: var(--surface-color) !important;
        }
        
        /* 侧边栏分隔线 */
        section[data-testid="stSidebar"] hr,
        .css-1d391kg hr {
            border-color: var(--border-color) !important;
        }
        
        /* 强制覆盖所有侧边栏文本元素 */
        .css-1d391kg * {
            color: var(--text-color) !important;
        }
        
        /* 但保持按钮文字为白色 */
        .css-1d391kg .stButton > button * {
            color: white !important;
        }

        /* 全局表单元素对比度修复 */
        div[data-testid="stSelectbox"] > label,
        div[data-testid="stSelectbox"] > div > div,
        div[data-testid="stSelectbox"] > div > div > div,
        div[data-testid="stSelectbox"] span {
            color: var(--text-color) !important;
            background-color: var(--surface-color) !important;
        }

        /* 下拉菜单选项 */
        ul[role="listbox"] li,
        div[role="option"] {
            color: var(--text-color) !important;
            background-color: var(--surface-color) !important;
        }

        /* 输入框通用样式 */
        input[type="text"], input[type="number"], textarea, select {
            color: var(--text-color) !important;
            background-color: var(--surface-color) !important;
            border-color: var(--border-color) !important;
        }

        /* 标签通用样式 */
        label, .stCaption {
            color: var(--text-color) !important;
        }
    </style>
    """

def apply_theme():
    """应用当前选择的主题"""
    # 确保session_state中有主题设置
    if 'current_theme' not in st.session_state:
        st.session_state.current_theme = "亮色主题"
    
    # 根据选择的主题应用CSS
    if st.session_state.current_theme == "暗色主题":
        st.markdown(get_dark_theme_css(), unsafe_allow_html=True)
    else:
        st.markdown(get_light_theme_css(), unsafe_allow_html=True)

def get_plotly_theme():
    """获取Plotly图表主题配置"""
    if 'current_theme' not in st.session_state:
        st.session_state.current_theme = "亮色主题"
    
    if st.session_state.current_theme == "暗色主题":
        return {
            'layout': {
                'paper_bgcolor': '#2d2d2d',
                'plot_bgcolor': '#1e1e1e',
                'font': {'color': '#ffffff'},
                'colorway': ['#4a9eff', '#ff9f40', '#4caf50', '#f44336', '#00bcd4', '#9c27b0', '#ff5722'],
                'xaxis': {'gridcolor': '#404040'},
                'yaxis': {'gridcolor': '#404040'}
            }
        }
    else:
        return {
            'layout': {
                'paper_bgcolor': '#ffffff',
                'plot_bgcolor': '#f8f9fa',
                'font': {'color': '#333333'},
                'colorway': ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#17becf', '#9467bd', '#8c564b'],
                'xaxis': {'gridcolor': '#e0e0e0'},
                'yaxis': {'gridcolor': '#e0e0e0'}
            }
        } 