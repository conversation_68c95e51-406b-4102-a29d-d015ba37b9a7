#!/usr/bin/env python3
"""
检查测试文件的内容和结构
"""

import pandas as pd
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from solution0606 import ckfile as original_ckfile


def check_test_file():
    """检查测试文件的结构"""
    chemkin_file = "TEST/test1_results.xlsm"
    
    try:
        print("检查CHEMKIN测试文件结构...")
        
        # 直接读取Excel文件查看所有sheet
        xlsx = pd.ExcelFile(chemkin_file)
        print(f"所有工作表: {xlsx.sheet_names}")
        
        # 使用原始ckfile检查
        chemkin = original_ckfile(chemkin_file)
        chemkin.load_chemkin_file()
        
        print(f"\n检测到的工作表类型:")
        print(f"  soln_sheets: {len(chemkin.soln_sheets)} 个")
        if chemkin.soln_sheets:
            print(f"    {chemkin.soln_sheets[:3]}...")
            
        print(f"  end_point_sheets: {len(chemkin.end_point_sheets)} 个")
        if chemkin.end_point_sheets:
            print(f"    {chemkin.end_point_sheets[:3]}...")
            
        print(f"  rop_sheets: {len(chemkin.rop_sheets)} 个")
        if chemkin.rop_sheets:
            print(f"    {chemkin.rop_sheets[:3]}...")
            
        print(f"  sensitivity_sheets: {len(chemkin.sensitivity_sheets)} 个")
        if chemkin.sensitivity_sheets:
            print(f"    {chemkin.sensitivity_sheets[:3]}...")
        
        print(f"\n功能标志:")
        print(f"  mole_fraction_exist: {chemkin.mole_fraction_exist}")
        print(f"  net_reaction_rate_exist: {chemkin.net_reaction_rate_exist}")
        print(f"  sensitivity_exist: {chemkin.sensitivity_exist}")
        print(f"  ROP_exist: {chemkin.ROP_exist}")
        
        print(f"\n变量信息:")
        print(f"  variables: {chemkin.variables}")
        print(f"  variable_name: {chemkin.variable_name}")
        
        # 检查第一个soln sheet的内容
        if chemkin.soln_sheets:
            first_sheet = chemkin.soln_sheets[0]
            print(f"\n第一个soln sheet ({first_sheet}) 的列:")
            sheet_data = xlsx.parse(first_sheet, index_col=0)
            print(f"  总列数: {len(sheet_data.columns)}")
            
            # 分析列类型
            net_cols = [col for col in sheet_data.columns if 'Net' in col]
            temp_cols = [col for col in sheet_data.columns if 'Temperature' in col]
            pressure_cols = [col for col in sheet_data.columns if 'Pressure' in col]
            mole_cols = [col for col in sheet_data.columns if 'Mole_fraction' in col]
            
            print(f"  Net列: {len(net_cols)} 个")
            print(f"  Temperature列: {len(temp_cols)} 个")
            print(f"  Pressure列: {len(pressure_cols)} 个")
            print(f"  Mole_fraction列: {len(mole_cols)} 个")
            
            if net_cols:
                print(f"  前3个Net列: {net_cols[:3]}")
            if mole_cols:
                print(f"  前3个Mole_fraction列: {mole_cols[:3]}")
        
    except Exception as e:
        print(f"❌ 检查过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    check_test_file()
