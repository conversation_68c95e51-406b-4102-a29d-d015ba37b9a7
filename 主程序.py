import streamlit as st
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import os
import sys

# 添加src目录到路径
sys.path.append('src')
from theme_manager import theme_selector, apply_theme

# 尝试使用优化版本，如果失败则使用原版本
try:
    from src.optimized import solution, ckfile
    sl = type('module', (), {
        'solution': solution,
        'ckfile': ckfile
    })()
    print("使用优化版本的solution模块")
except ImportError:
    import solution0606 as sl
    print("使用原版本的solution模块")

# 使用 Streamlit 创建菜单项（使用下拉选项作为示例）
st.set_page_config(layout="wide", page_title="ROP分析工具箱", page_icon="🔬")

# 应用主题
apply_theme()

# 主题选择器
theme_selector()
menu_selection = st.sidebar.selectbox('菜单', ['首页', '帮助', '关于','加油!'])
# 根据选择处理不同的页面
st.title('ROP分析工具箱V5.0 IET CC')
if menu_selection == '帮助':
    os.startfile('教程.docx')
elif menu_selection == '关于':
    st.subheader("中科院工程热物理研究所田振玉研究员团队 \n 程序编写：王杜 <EMAIL>  V5.0")
elif menu_selection == '加油!':
    st.toast(':rainbow[CnF在向你招手！加油!]')
    st.balloons()


# 文件上传器替代文件浏览输入框
file1 = st.file_uploader("请选择chemkin前处理gas.out 文件", type=['out'])


# 按钮和文本显示
if st.button('前处理'):
    if 'b' in st.session_state:
        st.warning('注意重新前处理后必须后处理！否者结果会出现错误')
    if file1:
        a = sl.solution(file1)
        a.process_gas_out(True)
        st.session_state['a'] = a
        if 'a' in st.session_state:
            st.success("处理成功")
        else:
            st.error("前处理失败！请检查上述报错信息")
    else:
        st.error('没有选择前处理文件')

file2 = st.file_uploader("请选择chemkin结果文件xlsx或xlsm", type=['xlsm', 'xlsx'])
if st.button('后处理'):
    if 'a' not in st.session_state:
        st.error("请先进行前处理！")
    elif file2:
        b = sl.ckfile(file2)
        b.load_chemkin_file()
        b.combine_sheets(st.session_state['a'], True)
        st.session_state['b'] = b
        if 'b' in st.session_state:
            st.success("后处理成功")
        else:
            st.error("后处理失败！请检查上述报错信息")
    else:
        st.error('没有选择后处理文件')
# 其他按钮
