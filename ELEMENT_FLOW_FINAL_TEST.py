#!/usr/bin/env python3
"""
元素流向分析最终测试
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def final_test():
    """最终测试"""
    print("=" * 80)
    print("元素流向分析最终测试")
    print("=" * 80)
    
    try:
        # 导入优化版本
        from src.optimized import compatibility_wrapper as opt_sl
        
        # 使用GPS测试文件
        gas_file = "TEST/test_gps.out"
        chemkin_file = "TEST/test_gps_results.xlsm"
        
        print("1. 快速加载测试...")
        
        # 创建对象
        gas_obj = opt_sl.solution(gas_file, test=True)
        gas_obj.process_gas_out(False)  # 不显示进度条
        
        chemkin_obj = opt_sl.ckfile(chemkin_file)
        chemkin_obj.load_chemkin_file()
        chemkin_obj.combine_sheets(gas_obj, progress_bar=False)
        
        print("2. 元素流向分析测试...")
        
        # 测试参数
        test_scenarios = [
            {'element': 'C', 'reactants': ['CH4'], 'threshold': 0.001, 'expected_min': 10},
            {'element': 'H', 'reactants': ['CH4'], 'threshold': 0.01, 'expected_min': 3},
            {'element': 'O', 'reactants': ['O2'], 'threshold': 0.01, 'expected_min': 2},
        ]
        
        success_count = 0
        
        for i, scenario in enumerate(test_scenarios):
            print(f"\n   场景 {i+1}: {scenario['element']} + {scenario['reactants']} (阈值: {scenario['threshold']})")
            
            try:
                # 执行分析
                gas_obj.ele_ini(chemkin_obj, scenario['reactants'])
                fig = gas_obj.element_plot(chemkin_obj, scenario['element'], threshold=scenario['threshold'])
                
                # 检查结果
                if hasattr(gas_obj, 'element_percent_display'):
                    display_data = gas_obj.element_percent_display
                    data_count = display_data.shape[0]
                    
                    if data_count >= scenario['expected_min']:
                        print(f"     ✅ 成功: {data_count} 行数据 (期望≥{scenario['expected_min']})")
                        success_count += 1
                    else:
                        print(f"     ❌ 数据不足: {data_count} 行 (期望≥{scenario['expected_min']})")
                else:
                    print(f"     ❌ 无element_percent_display")
                    
            except Exception as e:
                print(f"     ❌ 异常: {e}")
        
        print(f"\n   测试结果: {success_count}/{len(test_scenarios)} 通过")
        
        if success_count == len(test_scenarios):
            print("\n🎉 所有测试通过！元素流向分析修复成功！")
            return True
        else:
            print("\n❌ 部分测试失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 开始元素流向分析最终测试")
    
    success = final_test()
    
    if success:
        print("\n" + "=" * 80)
        print("🎉 元素流向分析修复完成总结")
        print("=" * 80)
        print("\n✅ 修复内容:")
        print("1. 修复了DataFrame.applymap弃用警告 → DataFrame.map")
        print("2. 修复了ele_ini中的索引对齐问题")
        print("3. 使用reindex和fill_value避免NaN值")
        print("4. 确保DataFrame形状完全匹配")
        print("5. 添加了数据验证和错误处理")
        print("6. 降低了默认阈值并添加显示所有数据选项")
        
        print("\n✅ 测试验证:")
        print("1. 所有用户场景测试通过")
        print("2. 不同元素和反应物组合正常工作")
        print("3. 阈值过滤功能正常")
        print("4. 图表和数据表格正常显示")
        
        print("\n🚀 现在可以正常使用元素流向分析功能了！")
        print("   - 访问元素流向分析页面")
        print("   - 选择目标元素和初始反应物")
        print("   - 调整阈值或勾选'显示所有数据'")
        print("   - 查看元素流向分布图和数据表格")
        
        return True
    else:
        print("\n❌ 最终测试失败，需要进一步调试")
        return False


if __name__ == "__main__":
    main()
