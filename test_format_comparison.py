#!/usr/bin/env python3
"""
测试脚本：比较原始combine_sheets和优化版combine_sheets_optimized的输出格式差异
"""

import sys
import os
import pandas as pd
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入原始版本
from solution0606 import solution as original_solution, ckfile as original_ckfile

# 导入优化版本
from src.optimized.compatibility_wrapper import solution as optimized_solution, ckfile as optimized_ckfile


def compare_data_structures(original_gas, optimized_gas, original_chemkin, optimized_chemkin):
    """比较两个版本的数据结构差异"""
    print("=" * 80)
    print("数据结构比较分析")
    print("=" * 80)
    
    # 比较gas_out对象的属性
    print("\n1. Gas对象属性比较:")
    gas_attrs = ['mole_fractions', 'integral_ROP', 'end_ROP', 'soln_collect', 'sensitivity_collect', 'rop_species_output']
    
    for attr in gas_attrs:
        orig_has = hasattr(original_gas, attr)
        opt_has = hasattr(optimized_gas, attr)
        print(f"  {attr:20} | 原始: {'✓' if orig_has else '✗'} | 优化: {'✓' if opt_has else '✗'}")
        
        if orig_has and opt_has:
            orig_val = getattr(original_gas, attr)
            opt_val = getattr(optimized_gas, attr)
            
            if isinstance(orig_val, dict) and isinstance(opt_val, dict):
                print(f"    -> 字典键数量: 原始={len(orig_val)}, 优化={len(opt_val)}")
                if orig_val and opt_val:
                    first_key = list(orig_val.keys())[0]
                    if first_key in opt_val:
                        orig_df = orig_val[first_key]
                        opt_df = opt_val[first_key]
                        if hasattr(orig_df, 'shape') and hasattr(opt_df, 'shape'):
                            print(f"    -> 第一个DataFrame形状: 原始={orig_df.shape}, 优化={opt_df.shape}")
                            if hasattr(orig_df, 'columns') and hasattr(opt_df, 'columns'):
                                print(f"    -> 列数量: 原始={len(orig_df.columns)}, 优化={len(opt_df.columns)}")
                                # 检查列名差异
                                orig_cols = set(orig_df.columns)
                                opt_cols = set(opt_df.columns)
                                if orig_cols != opt_cols:
                                    print(f"    -> ⚠️ 列名不同!")
                                    only_orig = orig_cols - opt_cols
                                    only_opt = opt_cols - orig_cols
                                    if only_orig:
                                        print(f"      仅原始版本有: {list(only_orig)[:5]}...")
                                    if only_opt:
                                        print(f"      仅优化版本有: {list(only_opt)[:5]}...")
            elif hasattr(orig_val, 'shape') and hasattr(opt_val, 'shape'):
                print(f"    -> DataFrame形状: 原始={orig_val.shape}, 优化={opt_val.shape}")
                if hasattr(orig_val, 'index') and hasattr(opt_val, 'index'):
                    print(f"    -> 索引相等: {orig_val.index.equals(opt_val.index)}")
                if hasattr(orig_val, 'columns') and hasattr(opt_val, 'columns'):
                    print(f"    -> 列名相等: {orig_val.columns.equals(opt_val.columns)}")
    
    # 检查优化版本特有的属性
    print("\n2. 优化版本特有属性:")
    opt_only_attrs = ['net_rate_collect', 'mole_fractions_max', 'possible_reactants', 'sensitivity_species_output']
    for attr in opt_only_attrs:
        if hasattr(optimized_gas, attr):
            val = getattr(optimized_gas, attr)
            if isinstance(val, dict):
                print(f"  {attr:25} | 字典，键数量: {len(val)}")
            elif hasattr(val, 'shape'):
                print(f"  {attr:25} | DataFrame形状: {val.shape}")
            elif isinstance(val, list):
                print(f"  {attr:25} | 列表，长度: {len(val)}")
            else:
                print(f"  {attr:25} | 类型: {type(val)}")


def detailed_soln_collect_comparison(original_gas, optimized_gas):
    """详细比较soln_collect的差异"""
    print("\n" + "=" * 80)
    print("soln_collect 详细比较")
    print("=" * 80)
    
    if not (hasattr(original_gas, 'soln_collect') and hasattr(optimized_gas, 'soln_collect')):
        print("⚠️ 其中一个版本缺少soln_collect属性")
        return
    
    orig_soln = original_gas.soln_collect
    opt_soln = optimized_gas.soln_collect
    
    print(f"工况数量: 原始={len(orig_soln)}, 优化={len(opt_soln)}")
    
    if orig_soln and opt_soln:
        first_key = list(orig_soln.keys())[0]
        if first_key in opt_soln:
            orig_df = orig_soln[first_key]
            opt_df = opt_soln[first_key]
            
            print(f"\n第一个工况 ({first_key}) 比较:")
            print(f"  形状: 原始={orig_df.shape}, 优化={opt_df.shape}")
            
            # 分析列类型
            orig_cols = orig_df.columns.tolist()
            opt_cols = opt_df.columns.tolist()
            
            print(f"  列数量: 原始={len(orig_cols)}, 优化={len(opt_cols)}")
            
            # 检查Net列
            orig_net_cols = [col for col in orig_cols if 'Net' in col]
            opt_net_cols = [col for col in opt_cols if 'Net' in col]
            print(f"  Net列数量: 原始={len(orig_net_cols)}, 优化={len(opt_net_cols)}")
            
            # 检查其他类型的列
            opt_temp_cols = [col for col in opt_cols if 'Temperature' in col]
            opt_pressure_cols = [col for col in opt_cols if 'Pressure' in col]
            opt_mole_cols = [col for col in opt_cols if 'Mole_fraction' in col]
            
            print(f"  优化版本额外列:")
            print(f"    Temperature列: {len(opt_temp_cols)}")
            print(f"    Pressure列: {len(opt_pressure_cols)}")
            print(f"    Mole_fraction列: {len(opt_mole_cols)}")
            
            # 这是关键差异！
            if len(orig_net_cols) == len(orig_cols) and len(opt_net_cols) < len(opt_cols):
                print(f"\n🔍 关键发现:")
                print(f"  原始版本soln_collect只包含Net反应速率列")
                print(f"  优化版本soln_collect包含完整Solution数据")
                print(f"  这可能是导致下游分析不兼容的主要原因!")


def test_format_differences():
    """测试两个版本的格式差异"""
    print("开始测试格式差异...")

    # 使用测试文件
    gas_file = "TEST/test1_gas.out"
    chemkin_file = "TEST/test1_results.xlsm"

    if not os.path.exists(gas_file) or not os.path.exists(chemkin_file):
        print(f"❌ 测试文件不存在: {gas_file} 或 {chemkin_file}")
        return

    try:
        print("1. 加载原始版本...")
        # 原始版本
        original_gas = original_solution(gas_file, test=True)
        original_gas.process_gas_out()

        original_chemkin = original_ckfile(chemkin_file)
        original_chemkin.load_chemkin_file()
        original_chemkin.combine_sheets(original_gas, progress_bar=False)

        print("2. 加载优化版本...")
        # 优化版本
        optimized_gas = optimized_solution(gas_file, test=True)
        optimized_gas.process_gas_out()

        optimized_chemkin = optimized_ckfile(chemkin_file, use_optimized=True)
        optimized_chemkin.load_chemkin_file()
        optimized_chemkin.combine_sheets_optimized(optimized_gas, progress_bar=False)

        print("3. 比较数据结构...")
        compare_data_structures(original_gas, optimized_gas, original_chemkin, optimized_chemkin)

        print("4. 详细比较soln_collect...")
        detailed_soln_collect_comparison(original_gas, optimized_gas)

        print("\n" + "=" * 80)
        print("测试完成!")
        print("=" * 80)

    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


def analyze_original_format(gas_obj):
    """分析原始版本的数据格式"""
    print("\n" + "=" * 80)
    print("原始版本数据格式分析")
    print("=" * 80)

    # 检查所有属性
    important_attrs = ['mole_fractions', 'integral_ROP', 'end_ROP', 'soln_collect', 'sensitivity_collect', 'rop_species_output']

    for attr in important_attrs:
        if hasattr(gas_obj, attr):
            val = getattr(gas_obj, attr)
            print(f"\n{attr}:")

            if isinstance(val, dict):
                print(f"  类型: 字典，键数量: {len(val)}")
                if val:
                    first_key = list(val.keys())[0]
                    first_val = val[first_key]
                    print(f"  第一个键: {first_key}")
                    if hasattr(first_val, 'shape'):
                        print(f"  第一个值形状: {first_val.shape}")
                        print(f"  第一个值列数: {len(first_val.columns)}")
                        # 分析列类型
                        cols = first_val.columns.tolist()
                        net_cols = [col for col in cols if 'Net' in col]
                        temp_cols = [col for col in cols if 'Temperature' in col]
                        pressure_cols = [col for col in cols if 'Pressure' in col]
                        mole_cols = [col for col in cols if 'Mole_fraction' in col]

                        print(f"    Net列数量: {len(net_cols)}")
                        print(f"    Temperature列数量: {len(temp_cols)}")
                        print(f"    Pressure列数量: {len(pressure_cols)}")
                        print(f"    Mole_fraction列数量: {len(mole_cols)}")

                        if len(net_cols) == len(cols):
                            print(f"    ✅ 只包含Net反应速率列")
                        else:
                            print(f"    ⚠️ 包含其他类型的列")

            elif hasattr(val, 'shape'):
                print(f"  类型: DataFrame，形状: {val.shape}")
                if hasattr(val, 'index'):
                    print(f"  索引: {val.index.tolist()}")
                if hasattr(val, 'columns'):
                    print(f"  列数: {len(val.columns)}")
                    print(f"  前5列: {val.columns[:5].tolist()}")
            elif isinstance(val, list):
                print(f"  类型: 列表，长度: {len(val)}")
                if val:
                    print(f"  前5个元素: {val[:5]}")
            else:
                print(f"  类型: {type(val)}")
        else:
            print(f"\n{attr}: ❌ 不存在")


if __name__ == "__main__":
    test_format_differences()
