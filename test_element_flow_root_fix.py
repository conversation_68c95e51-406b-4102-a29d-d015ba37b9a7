#!/usr/bin/env python3
"""
测试元素流向分析根本问题修复
使用TEST/test2_gas.out和TEST/test2_results.xlsm
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_root_fix():
    """测试根本问题修复"""
    print("=" * 80)
    print("测试元素流向分析根本问题修复")
    print("=" * 80)
    
    try:
        # 导入优化版本
        from src.optimized import compatibility_wrapper as opt_sl
        
        # 使用指定的测试文件
        gas_file = "TEST/test2_gas.out"
        chemkin_file = "TEST/test2_results.xlsm"
        
        print("1. 加载数据...")
        
        # 创建对象
        gas_obj = opt_sl.solution(gas_file, test=True)
        gas_obj.process_gas_out(False)  # 不显示进度条
        
        chemkin_obj = opt_sl.ckfile(chemkin_file)
        chemkin_obj.load_chemkin_file()
        chemkin_obj.combine_sheets(gas_obj, progress_bar=False)
        
        print("\n2. 验证数据结构...")
        
        # 检查mole_fractions
        print(f"   mole_fractions shape: {gas_obj.mole_fractions.shape}")
        print(f"   mole_fractions index: {list(gas_obj.mole_fractions.index)}")
        
        # 检查mole_fractions_max
        print(f"   mole_fractions_max shape: {gas_obj.mole_fractions_max.shape}")
        print(f"   mole_fractions_max index: {list(gas_obj.mole_fractions_max.index)}")
        
        # 验证列数一致性
        mole_fractions_cols = len(gas_obj.mole_fractions.columns)
        mole_fractions_max_cols = len(gas_obj.mole_fractions_max.columns)
        print(f"   mole_fractions列数: {mole_fractions_cols}")
        print(f"   mole_fractions_max列数: {mole_fractions_max_cols}")
        print(f"   列数匹配: {mole_fractions_cols == mole_fractions_max_cols}")
        
        # 检查possible_reactants
        print(f"   possible_reactants: {gas_obj.possible_reactants}")
        
        print("\n3. 测试元素流向分析...")
        
        # 测试参数
        test_scenarios = [
            {'element': 'C', 'reactants': ['CH4'], 'description': 'C元素 + CH4'},
            {'element': 'H', 'reactants': ['CH4'], 'description': 'H元素 + CH4'},
            {'element': 'O', 'reactants': ['O2'], 'description': 'O元素 + O2'},
        ]
        
        success_count = 0
        
        for i, scenario in enumerate(test_scenarios):
            print(f"\n   场景 {i+1}: {scenario['description']}")
            
            try:
                # 执行ele_ini
                gas_obj.ele_ini(chemkin_obj, scenario['reactants'])
                
                # 检查elements_initial
                if hasattr(gas_obj, 'elements_initial'):
                    elements_initial = gas_obj.elements_initial
                    print(f"     elements_initial shape: {elements_initial.shape}")
                    
                    if scenario['element'] in elements_initial.columns:
                        element_values = elements_initial.loc[:, scenario['element']]
                        has_nan = element_values.isna().any()
                        all_zero = (element_values == 0).all()
                        
                        print(f"     {scenario['element']} 元素值范围: {element_values.min():.6f} - {element_values.max():.6f}")
                        print(f"     是否有NaN: {has_nan}")
                        print(f"     是否全为零: {all_zero}")
                        
                        if not has_nan and not all_zero:
                            print(f"     ✅ ele_ini成功")
                            
                            # 测试element_plot
                            fig = gas_obj.element_plot(chemkin_obj, scenario['element'], threshold=0.001)
                            
                            if fig is not None and hasattr(gas_obj, 'element_percent_display'):
                                display_data = gas_obj.element_percent_display
                                if display_data.shape[0] > 0:
                                    print(f"     ✅ element_plot成功: {display_data.shape[0]} 行数据")
                                    success_count += 1
                                else:
                                    print(f"     ❌ element_plot无数据")
                            else:
                                print(f"     ❌ element_plot失败")
                        else:
                            print(f"     ❌ ele_ini数据异常")
                    else:
                        print(f"     ❌ {scenario['element']} 不在 elements_initial.columns 中")
                else:
                    print(f"     ❌ elements_initial 不存在")
                    
            except Exception as e:
                print(f"     ❌ 异常: {e}")
        
        print(f"\n   测试结果: {success_count}/{len(test_scenarios)} 通过")
        
        if success_count == len(test_scenarios):
            print("\n🎉 根本问题修复成功！")
            return True
        else:
            print("\n❌ 仍有问题需要解决")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🚀 开始测试元素流向分析根本问题修复")
    
    success = test_root_fix()
    
    if success:
        print("\n" + "=" * 80)
        print("🎉 根本问题修复成功总结")
        print("=" * 80)
        print("\n✅ 修复内容:")
        print("1. 从mole_fractions计算mole_fractions_max，不依赖max_sheets")
        print("2. mole_fractions_max现在是单行矩阵，索引为'max_values'")
        print("3. 修复了ele_ini方法，正确处理单行mole_fractions_max")
        print("4. 消除了索引不匹配导致的NaN值问题")
        print("5. 确保了数据结构的一致性")
        
        print("\n✅ 数据结构:")
        print("- mole_fractions: (工况数 × 组分数)")
        print("- mole_fractions_max: (1 × 组分数) - 每个组分的最大摩尔分数")
        print("- elements_initial: (工况数 × 元素数) - 每个工况下每个元素的初始量")
        
        print("\n🚀 现在元素流向分析应该能正常工作了！")
        return True
    else:
        print("\n❌ 根本问题修复失败，需要进一步调试")
        return False


if __name__ == "__main__":
    main()
