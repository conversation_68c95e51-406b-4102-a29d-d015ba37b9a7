#!/usr/bin/env python3
"""
测试元素流向分析修复
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from solution0606 import solution as original_solution, ckfile as original_ckfile


def test_element_flow_data():
    """测试元素流向分析数据"""
    print("=" * 80)
    print("测试元素流向分析数据")
    print("=" * 80)
    
    try:
        # 使用GPS测试文件
        gas_file = "TEST/test_gps.out"
        chemkin_file = "TEST/test_gps_results.xlsm"
        
        print("1. 加载测试数据...")
        
        # 加载gas数据
        gas_obj = original_solution(gas_file, test=True)
        gas_obj.process_gas_out()
        
        # 加载chemkin数据
        chemkin_obj = original_ckfile(chemkin_file)
        chemkin_obj.load_chemkin_file()
        chemkin_obj.combine_sheets(gas_obj, progress_bar=False)
        
        print("2. 检查元素数据结构...")
        
        # 检查elements属性
        if hasattr(gas_obj, 'elements') and gas_obj.elements is not None:
            print(f"   ✅ elements属性存在: {gas_obj.elements}")
        else:
            print("   ❌ elements属性缺失或为None")
        
        # 检查species_element属性
        if hasattr(gas_obj, 'species_element') and gas_obj.species_element is not None:
            print(f"   ✅ species_element属性存在")
            print(f"   📊 species_element形状: {gas_obj.species_element.shape}")
            print(f"   📊 元素列: {list(gas_obj.species_element.columns)}")
            print(f"   📊 组分数: {len(gas_obj.species_element.index)}")
            
            # 检查数据类型
            print(f"   📊 数据类型: {gas_obj.species_element.dtypes.iloc[0]}")
            
            # 显示前几行数据
            print("   📊 前5行数据:")
            print(gas_obj.species_element.head())
            
        else:
            print("   ❌ species_element属性缺失或为None")
        
        print("3. 测试元素流向分析功能...")
        
        # 测试ele_ini方法
        try:
            # 选择一些反应物进行测试
            test_reactants = gas_obj.species[:5]  # 前5个组分
            gas_obj.ele_ini(chemkin_obj, test_reactants)
            
            if hasattr(gas_obj, 'elements_initial'):
                print(f"   ✅ ele_ini方法成功执行")
                print(f"   📊 elements_initial形状: {gas_obj.elements_initial.shape}")
            else:
                print("   ❌ ele_ini方法执行失败")
                
        except Exception as e:
            print(f"   ❌ ele_ini方法执行失败: {str(e)}")
        
        # 测试element_plot方法
        try:
            if gas_obj.elements and len(gas_obj.elements) > 0:
                test_element = gas_obj.elements[0]  # 第一个元素
                fig = gas_obj.element_plot(chemkin_obj, test_element, threshold=0.05)
                
                if fig is not None:
                    print(f"   ✅ element_plot方法成功执行 (元素: {test_element})")
                else:
                    print(f"   ❌ element_plot方法返回None (元素: {test_element})")
            else:
                print("   ❌ 无可用元素进行测试")
                
        except Exception as e:
            print(f"   ❌ element_plot方法执行失败: {str(e)}")
        
        print("4. 检查数据完整性...")
        
        # 检查关键属性
        key_attributes = [
            'species', 'reactions', 'elements', 'species_element', 'species_MW',
            'stoichimetric', 'mole_fractions'
        ]
        
        for attr in key_attributes:
            if hasattr(gas_obj, attr) and getattr(gas_obj, attr) is not None:
                value = getattr(gas_obj, attr)
                if hasattr(value, 'shape'):
                    print(f"   ✅ {attr}: {value.shape}")
                elif hasattr(value, '__len__'):
                    print(f"   ✅ {attr}: 长度 {len(value)}")
                else:
                    print(f"   ✅ {attr}: 存在")
            else:
                print(f"   ❌ {attr}: 缺失")
        
        print("✅ 元素流向分析数据测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 元素流向分析数据测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_gps_element_compatibility():
    """测试GPS与元素流向分析的兼容性"""
    print("\n" + "=" * 80)
    print("测试GPS与元素流向分析兼容性")
    print("=" * 80)
    
    try:
        from src.gps_integration import IntegratedGPSAnalyzer
        
        # 使用GPS测试文件
        gas_file = "TEST/test_gps.out"
        chemkin_file = "TEST/test_gps_results.xlsm"
        
        print("1. 加载数据并运行GPS分析...")
        
        # 加载gas数据
        gas_obj = original_solution(gas_file, test=True)
        gas_obj.process_gas_out()
        
        # 加载chemkin数据
        chemkin_obj = original_ckfile(chemkin_file)
        chemkin_obj.load_chemkin_file()
        chemkin_obj.combine_sheets(gas_obj, progress_bar=False)
        
        # 运行GPS分析
        gps_analyzer = IntegratedGPSAnalyzer()
        success = gps_analyzer.load_project_data(
            gas_obj=gas_obj,
            condition_key=None,
            use_end_point=False
        )
        
        if success:
            print("   ✅ GPS数据加载成功")
        else:
            print("   ❌ GPS数据加载失败")
            return False
        
        print("2. 检查GPS分析后的元素数据...")
        
        # 检查GPS分析是否影响了原始数据
        if hasattr(gas_obj, 'elements') and gas_obj.elements is not None:
            print(f"   ✅ GPS分析后elements属性仍存在: {gas_obj.elements}")
        else:
            print("   ❌ GPS分析后elements属性丢失")
        
        if hasattr(gas_obj, 'species_element') and gas_obj.species_element is not None:
            print(f"   ✅ GPS分析后species_element属性仍存在")
            print(f"   📊 形状: {gas_obj.species_element.shape}")
        else:
            print("   ❌ GPS分析后species_element属性丢失")
        
        print("3. 测试GPS分析后的元素流向分析...")
        
        # 测试元素流向分析是否仍然工作
        try:
            test_reactants = gas_obj.species[:3]
            gas_obj.ele_ini(chemkin_obj, test_reactants)
            
            if gas_obj.elements and len(gas_obj.elements) > 0:
                test_element = gas_obj.elements[0]
                fig = gas_obj.element_plot(chemkin_obj, test_element, threshold=0.05)
                
                if fig is not None:
                    print(f"   ✅ GPS分析后元素流向分析仍正常工作")
                else:
                    print(f"   ❌ GPS分析后元素流向分析返回None")
            else:
                print("   ❌ GPS分析后无可用元素")
                
        except Exception as e:
            print(f"   ❌ GPS分析后元素流向分析失败: {str(e)}")
        
        print("✅ GPS与元素流向分析兼容性测试完成")
        return True
        
    except Exception as e:
        print(f"❌ GPS与元素流向分析兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始元素流向分析修复测试")
    
    results = []
    
    # 运行各项测试
    results.append(test_element_flow_data())
    results.append(test_gps_element_compatibility())
    
    # 总结结果
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！元素流向分析功能正常")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 元素流向分析功能正常!")
    else:
        print("\n❌ 发现问题，需要进一步调试")
