import streamlit as st
import sys

# 添加src目录到路径
sys.path.append('src')
from theme_manager import theme_selector, apply_theme, get_plotly_theme

import os
import sys
from tempfile import NamedTemporaryFile

# 添加新模块路径（Windows兼容）
current_dir = os.path.dirname(os.path.abspath(__file__))
src_path = os.path.join(current_dir, '..', 'src')
if src_path not in sys.path:
    sys.path.insert(0, src_path)

# 导入重构后的模块
from analyzers.comparison_analyzer import ComparisonAnalyzer, DataProcessor
from core.exceptions import ChemkinProcessingError

# 保持对legacy模块的向后兼容
import solution0606 as sl

# ========================================
# 页面配置和现代化样式
# ========================================

st.set_page_config(layout="wide", page_title="结果快速对比", page_icon="📊")

# 应用主题
apply_theme()

# 主题选择器
theme_selector()

# 现代化CSS样式
st.markdown("""
<style>

    /* 现代化卡片样式 */
    .analysis-card {
        background: linear-gradient(135deg, rgba(31, 119, 180, 0.1), rgba(23, 190, 207, 0.1));
        padding: 1.5rem;
        border-radius: 15px;
        border-left: 4px solid var(--primary-color);
        box-shadow: 0 4px 6px var(--shadow-color);
        margin: 1rem 0;
    }
    
    .parameter-card {
        background: var(--surface-color);
        padding: 1.2rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px var(--shadow-color);
        border: 1px solid var(--border-color);
        margin: 0.5rem 0;
    }
    
    .success-card {
        background: linear-gradient(135deg, rgba(44, 160, 44, 0.1), rgba(23, 190, 207, 0.1));
        padding: 1.5rem;
        border-radius: 15px;
        border-left: 4px solid var(--success-color);
        box-shadow: 0 4px 6px var(--shadow-color);
        margin: 1rem 0;
    }
    
    .warning-card {
        background: linear-gradient(135deg, rgba(255, 127, 14, 0.1), rgba(31, 119, 180, 0.1));
        padding: 1.5rem;
        border-radius: 15px;
        border-left: 4px solid var(--secondary-color);
        box-shadow: 0 4px 6px var(--shadow-color);
        margin: 1rem 0;
    }
    
    /* 现代化按钮样式 */
    .stButton > button {
        background: linear-gradient(90deg, var(--primary-color), var(--info-color));
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.6rem 1.5rem;
        font-weight: 600;
        box-shadow: 0 2px 4px var(--shadow-color);
        transition: all 0.3s ease;
    }
    
    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px var(--shadow-hover);
    }
    
    /* 下载按钮特殊样式 */
    .stDownloadButton > button {
        background: linear-gradient(90deg, var(--success-color), var(--info-color));
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.6rem 1.5rem;
        font-weight: 600;
        box-shadow: 0 2px 4px var(--shadow-color);
        transition: all 0.3s ease;
    }
    
    .stDownloadButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px var(--shadow-hover);
    }
    
    /* 文件上传器样式 */
    .stFileUploader > div > div {
        border-radius: 10px;
        border: 2px dashed #d0d7de;
        padding: 1rem;
        background: rgba(248, 249, 250, 0.5);
    }
    
    .stFileUploader > div > div:hover {
        border-color: var(--primary-color);
        background: rgba(31, 119, 180, 0.05);
    }
    
    /* 输入控件样式 */
    .stTextInput > div > div > input {
        border-radius: 8px;
        border: 1px solid var(--border-color);
    }
    
    /* 进度条样式 */
    .stProgress > div > div > div {
        background: linear-gradient(90deg, var(--primary-color), var(--info-color));
        border-radius: 4px;
    }
</style>
""", unsafe_allow_html=True)

# ========================================
# 页面标题
# ========================================

st.markdown("""
<div class="analysis-card">
    <h1 style="margin: 0; color: var(--primary-color);">📊 结果快速对比</h1>
    <p style="margin: 0.5rem 0 0 0; color: var(--text-secondary);">
        实验与模拟结果对比分析 - 快速生成对比图表和数据分析
    </p>
</div>
""", unsafe_allow_html=True)

# ========================================
# 文件上传区域
# ========================================

with st.expander("📁 文件上传设置", expanded=True):
    st.markdown('<div class="parameter-card">', unsafe_allow_html=True)
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### 📈 实验结果文件")
        file_exp = st.file_uploader(
            "选择实验结果文件", 
            type=['xlsx', 'xlsm'],
            help="上传包含实验数据的Excel文件"
        )
        
        if file_exp:
            st.success(f"✅ 已上传实验文件: {file_exp.name}")
    
    with col2:
        st.markdown("#### 💻 模拟结果文件")
        files_sim = st.file_uploader(
            "选择模拟结果文件(可多选)", 
            type=['xlsx', 'xlsm'],
            accept_multiple_files=True,
            help="上传一个或多个模拟结果文件"
        )
        
        if files_sim:
            st.success(f"✅ 已上传 {len(files_sim)} 个模拟文件")
            for i, file in enumerate(files_sim, 1):
                st.write(f"  {i}. {file.name}")
    
    st.markdown('</div>', unsafe_allow_html=True)

# ========================================
# 数据格式说明
# ========================================

with st.expander("ℹ️ 文件格式要求", expanded=False):
    st.markdown("""
### 📋 文件格式要求
    
    **实验结果文件格式**：
    - **首列**: 图中横坐标数据（如时间、温度、距离等）
    - **首行**: 组分名称（应与模拟中保持一致）
    - **数据区域**: 对应组分的实验测量值
    
    **模拟结果文件格式**：
    - 与实验文件格式保持一致
    - 支持多个模拟文件同时对比
    - 组分名称必须与实验文件中的名称一致
    
    **支持格式**：
    - Excel文件 (.xlsx, .xlsm)
    - 确保数据完整性和格式一致性
    """)

# ========================================
# 输出设置
# ========================================

with st.expander("📂 输出设置", expanded=True):
    st.markdown('<div class="parameter-card">', unsafe_allow_html=True)
    
    col1, col2 = st.columns([3, 1])
    
    with col1:
        path = st.text_input(
            '**结果储存目录**',
            value=os.getcwd(),
            help="指定对比图表的保存目录"
        )
    
    with col2:
        debug_mode = st.checkbox(
            '**调试模式**',
            value=False,
            help="开启后显示详细的处理过程信息"
        )
    
    if path and os.path.exists(path):
        st.success(f"✅ 输出目录: {path}")
    elif path:
        st.warning(f"⚠️ 目录不存在: {path}")
    
    if debug_mode:
        st.info("🔧 调试模式已开启 - 将显示详细的处理信息")
    
    st.markdown('</div>', unsafe_allow_html=True)

# ========================================
# 横坐标变量选择（针对没有end_point sheets的文件）
# ========================================

# 检查是否需要选择横坐标变量
need_x_selection = False
available_x_vars = []
selected_x_var = None
selected_x_data = None
point_type = 'first'  # 默认使用第一点

if files_sim:
    # 检查模拟文件是否需要横坐标选择
    check_result = ComparisonAnalyzer.check_files_need_extraction(files_sim)
    
    if check_result['need_extraction']:
        need_x_selection = True
        
        with st.expander("🎯 横坐标变量选择", expanded=True):
            st.markdown('<div class="parameter-card">', unsafe_allow_html=True)
            
            if debug_mode:  # 只有在调试模式下才显示这些信息
                st.info(f"检测到 {len(check_result['files_without_endpoints'])} 个模拟文件没有end_point数据表，需要选择横坐标变量：")
                
                # 显示需要处理的文件
                st.markdown("**需要处理的文件：**")
                for filename in check_result['files_without_endpoints']:
                    st.write(f"  • {filename}")
                
                if check_result['files_with_endpoints']:
                    st.markdown("**有end_point数据的文件：**")
                    for filename in check_result['files_with_endpoints']:
                        st.write(f"  • {filename}")
            else:
                st.info("⚙️ 检测到部分文件需要选择横坐标变量，请在下方进行选择")
            
            # 获取可用的横坐标变量名称
            available_x_vars = ComparisonAnalyzer.get_variable_names_from_soln_sheets(files_sim)
            
            if available_x_vars:
                col1_x, col2_x = st.columns(2)
                
                with col1_x:
                    selected_x_var = st.selectbox(
                        "**选择横坐标变量：**",
                        options=available_x_vars,
                        help="选择用作横坐标的变量",
                        key="comparison_x_var"
                    )
                
                with col2_x:
                    point_type = st.radio(
                        "**数据点类型：**",
                        options=['first', 'last'],
                        format_func=lambda x: '第一点' if x == 'first' else '最后一点',
                        help="选择使用每个工况的第一点还是最后一点数据",
                        key="comparison_point_type"
                    )
                
                # 根据选择获取对应的横坐标数据
                if selected_x_var and point_type:
                    x_axis_variables = ComparisonAnalyzer.get_available_x_axis_variables_with_point_type(files_sim, point_type)
                    
                    if selected_x_var in x_axis_variables:
                        selected_x_data = x_axis_variables[selected_x_var]
                        
                        if debug_mode:  # 只有在调试模式下才显示详细预览
                            st.markdown("**横坐标数据预览：**")
                            st.write(f"数据点数: {len(selected_x_data)}")
                            st.write(f"数值范围: {min(selected_x_data):.3f} - {max(selected_x_data):.3f}")
                            
                            # 显示前几个数据点
                            if len(selected_x_data) <= 5:
                                st.write(f"数据值: {selected_x_data}")
                            else:
                                st.write(f"前5个值: {selected_x_data[:5]}")
                        
                        st.success(f"✅ 已选择横坐标变量: {selected_x_var} ({point_type}点) - {len(selected_x_data)}个数据点")
                    else:
                        st.error(f"所选变量 {selected_x_var} 在 {point_type} 点数据中不可用")
            else:
                st.error("❌ 未找到可用的横坐标变量")
                st.markdown("""
**可能的原因：**
                - 文件中的所有变量都是摩尔分数或反应速率数据
                - 变量值为常数（变化小于1%）
                - 文件格式不符合要求
                - soln_sheets数据结构异常
                """)
            
            st.markdown('</div>', unsafe_allow_html=True)

# ========================================
# 处理按钮和结果
# ========================================

if st.button('🚀 开始结果对比分析', key='compare_analysis'):
    if file_exp is None or not files_sim:
        st.markdown("""
<div class="warning-card">
            <h3 style="color: var(--secondary-color); margin: 0;">⚠️ 请上传必要文件</h3>
            <p style="margin: 0.5rem 0 0 0;">
                请确保已上传实验结果文件和至少一个模拟结果文件。
            </p>
        </div>
        """, unsafe_allow_html=True)
    elif need_x_selection and (not selected_x_var or not selected_x_data):
        st.markdown("""
<div class="warning-card">
            <h3 style="color: var(--secondary-color); margin: 0;">⚠️ 请选择横坐标变量</h3>
            <p style="margin: 0.5rem 0 0 0;">
                检测到模拟文件没有end_point数据表，请选择合适的横坐标变量后再进行分析。
            </p>
        </div>
        """, unsafe_allow_html=True)
    else:
        with st.spinner('正在处理结果对比，请稍候...'):
            try:
                # 首先验证输入文件
                validation_result = ComparisonAnalyzer.validate_input_files(file_exp, files_sim)
                
                if not validation_result['valid']:
                    st.error("❌ 文件验证失败:")
                    for error in validation_result['errors']:
                        st.error(f"  • {error}")
                    st.stop()
                
                # 显示验证警告
                if validation_result['warnings']:
                    for warning in validation_result['warnings']:
                        st.warning(f"⚠️ {warning}")
                
                # 使用重构的分析器处理对比分析
                data_plot = ComparisonAnalyzer.create_comparison_plot(
                    file_exp, 
                    files_sim, 
                    path, 
                    progress_bar=True,
                    selected_x_var=selected_x_var,
                    selected_x_data=selected_x_data,
                    debug_mode=debug_mode
                )
                
                if data_plot is None:
                    st.error("❌ 对比分析失败，请检查文件格式和内容")
                    st.stop()
                
                # 显示成功消息
                if need_x_selection:
                    success_msg = f"""
                    <div class="success-card">
                        <h3 style="color: var(--success-color); margin: 0;">✅ 对比分析完成</h3>
                        <p style="margin: 0.5rem 0 0 0;">
                            处理成功！使用横坐标变量: <strong>{selected_x_var}</strong> ({point_type}点)<br>
                            图像结果已储存在 <strong>{path}</strong>
                        </p>
                    </div>
                    """
                else:
                    success_msg = f"""
                    <div class="success-card">
                        <h3 style="color: var(--success-color); margin: 0;">✅ 对比分析完成</h3>
                        <p style="margin: 0.5rem 0 0 0;">
                            处理成功！图像结果已储存在 <strong>{path}</strong>
                        </p>
                    </div>
                    """
                
                st.markdown(success_msg, unsafe_allow_html=True)
                
                # 生成Excel文件
                ComparisonAnalyzer.save_comparison_data('结果文件.xlsx', data_plot, debug_mode=debug_mode)
                
                # 显示结果统计
                st.markdown("### 📈 处理结果统计")
                
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.metric("实验文件", "1个", delta="已处理")
                
                with col2:
                    st.metric("模拟文件", f"{len(files_sim)}个", delta="已处理")
                
                with col3:
                    if need_x_selection:
                        st.metric("横坐标变量", selected_x_var, delta="已应用")
                    else:
                        st.metric("输出图表", "已生成", delta="保存完成")
                
                # 提供下载
                st.markdown("### 📥 结果下载")
                
                with open('结果文件.xlsx', 'rb') as file:
                    st.download_button(
                        label="📥 下载Excel分析结果",
                        data=file,
                        file_name='实验模拟对比结果.xlsx',
                        mime='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                        help="下载包含对比分析结果的Excel文件"
                    )
                
                # 清理临时文件
                if os.path.exists('结果文件.xlsx'):
                    os.unlink('结果文件.xlsx')
                    
            except Exception as e:
                st.error(f"❌ 处理过程中出现错误: {str(e)}")

# ========================================
# 使用说明
# ========================================

with st.expander("📖 使用说明", expanded=False):
    st.markdown("""
### 使用流程
    
    1. **📁 上传文件**
       - 上传实验结果文件（Excel格式）
       - 上传一个或多个模拟结果文件
    
    2. **🎯 横坐标变量选择（如需要）**
       - 如果模拟文件没有end_point数据表，系统会自动显示横坐标选择界面
       - 从可用变量中选择合适的横坐标（如Temperature、Pressure等）
       - 预览数据确保选择正确
    
    3. **📂 设置输出**
       - 指定图表保存目录
       - 确保目录存在且有写入权限
    
    4. **🚀 开始分析**
       - 点击"开始结果对比分析"按钮
       - 等待处理完成
    
    5. **📥 获取结果**
       - 图表自动保存到指定目录
       - 下载Excel格式的详细分析结果
    
    ### 🎨 图表优化功能
    
    **✨ 美观性提升**：
    - 🎨 **现代化配色**：使用科学可视化友好的配色方案
    - 📏 **优化布局**：改善子图间距和标题样式
    - 🔤 **字体优化**：使用Arial字体，提升可读性
    - 📊 **网格线美化**：半透明网格线，不影响数据展示
    - 💎 **标记样式**：实验数据使用钻石标记，模拟数据使用圆形标记
    
    **🎭 图例统一控制功能**：
    - 📌 **legendgroup技术**：使用Plotly的legendgroup参数实现统一控制
    - 🖱️ **一键控制**：点击图例中的"实验数据"可同时控制所有子图中的实验数据显示/隐藏
    - 🎯 **分组管理**：每个模拟文件形成一个图例组，点击可统一控制该文件在所有子图中的显示
    - 🔄 **交互友好**：保持Plotly原生的交互体验，支持双击隔离、拖拽缩放等功能
    
    **💡 使用技巧**：
    - 点击图例项可隐藏/显示对应的数据系列
    - 双击图例项可隔离显示该数据系列（隐藏其他）
    - 鼠标悬停显示详细的数据信息
    - 支持缩放、平移等交互操作
    
    ### 文件格式支持
    
    **标准格式（推荐）**：
    - **有end_point数据表**：系统自动识别并使用
    - **没有end_point数据表**：系统自动从solution数据表提取最后一点数据
    
    **数据要求**：
    - **实验文件**：首列为横坐标，首行为组分名称
    - **模拟文件**：包含solution数据表或end_point数据表
    - **组分一致性**：实验和模拟文件的组分名称必须匹配
    
    ### 横坐标变量选择说明
    
    **自动检测变量**：
    - 系统会自动扫描soln_sheets中的所有非摩尔分数和反应速率的变量
    - 包括但不限于：Temperature（温度）、Pressure（压力）、Distance（距离）、Time（时间）等
    - 只有变化大于1%的变量才会被列为候选
    
    **数据点类型**：
    - **最后一点**：使用每个工况soln_sheets的最后一行数据
    - **第一点**：使用每个工况soln_sheets的第一行数据
    
    **选择建议**：
    - 选择与实验横坐标一致的变量
    - 确保变量值有合理的变化范围（变化>1%）
    - 数据点数应与实验数据点数匹配
    - 根据实验条件选择合适的数据点类型
    
    ### 注意事项
    
    - **数据一致性**: 确保实验和模拟文件的组分名称一致
    - **文件格式**: 仅支持Excel格式(.xlsx, .xlsm)
    - **存储空间**: 确保输出目录有足够的存储空间
    - **处理时间**: 文件较大时需要更长处理时间
    - **混合模式**: 支持部分文件有end_point、部分文件只有solution数据的混合情况
    
    ### 输出内容
    
    - **对比图表**: HTML格式的交互式对比图
    - **分析数据**: Excel格式的详细数据对比
    - **统计信息**: 对比分析的统计结果
    - **变量信息**: 使用的横坐标变量详情
    
    ### ⚡ 图表交互说明
    
    **图例控制（核心功能）**：
    - 🖱️ **单击图例**：显示/隐藏对应数据在**所有子图**中的轨迹
    - 🖱️ **双击图例**：隔离显示该数据（隐藏其他所有数据）
    - 🔄 **再次双击**：恢复显示所有数据
    
    **缩放和平移**：
    - 🖱️ **拖拽**：平移图表
    - 🖱️ **滚轮**：缩放图表
    - 📏 **框选**：精确缩放到选定区域
    - 🏠 **重置**：使用工具栏的"重置坐标轴"按钮
    
    **数据详情**：
    - 💬 **悬停提示**：显示精确的数值和标签
    - 📊 **科学计数法**：自动格式化小数值
    """)

# ========================================
# 页脚信息
# ========================================

st.markdown("---")
st.markdown("""
<div style="text-align: center; color: var(--text-secondary); padding: 1rem;">
    <p>📊 结果快速对比 | ROP分析工具箱 V6.0</p>
</div>
""", unsafe_allow_html=True)