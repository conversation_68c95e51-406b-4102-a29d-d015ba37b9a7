#!/usr/bin/env python3
"""
集成测试：验证优化版本与主应用的兼容性
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入优化版本
from src.optimized.compatibility_wrapper import solution as optimized_solution, ckfile as optimized_ckfile


def test_integration():
    """测试优化版本与主应用的集成"""
    print("开始集成测试...")
    
    # 使用测试文件
    gas_file = "TEST/test1_gas.out"
    chemkin_file = "TEST/test1_results.xlsm"
    
    if not os.path.exists(gas_file) or not os.path.exists(chemkin_file):
        print(f"❌ 测试文件不存在: {gas_file} 或 {chemkin_file}")
        return
    
    try:
        print("1. 测试优化版本的完整流程...")
        
        # 模拟主应用的调用流程
        a = optimized_solution(gas_file, test=True)
        a.process_gas_out()
        
        b = optimized_ckfile(chemkin_file, use_optimized=True)
        b.load_chemkin_file()
        
        # 这是主应用中的关键调用
        b.combine_sheets_optimized(a, progress_bar=False)
        
        print("2. 验证关键数据结构...")
        
        # 验证关键属性存在
        required_attrs = ['integral_ROP', 'end_ROP', 'rop_species_output']
        for attr in required_attrs:
            if hasattr(a, attr):
                val = getattr(a, attr)
                if isinstance(val, dict):
                    print(f"  ✅ {attr}: 字典，{len(val)} 个工况")
                elif isinstance(val, list):
                    print(f"  ✅ {attr}: 列表，{len(val)} 个元素")
                else:
                    print(f"  ✅ {attr}: {type(val)}")
            else:
                print(f"  ❌ {attr}: 缺失")
        
        # 验证数据格式
        if hasattr(a, 'integral_ROP') and a.integral_ROP:
            first_key = list(a.integral_ROP.keys())[0]
            first_rop = a.integral_ROP[first_key]
            print(f"  ✅ integral_ROP 格式: {type(first_rop)}, 形状: {first_rop.shape}")
            
        # 验证soln_collect（如果存在）
        if hasattr(a, 'soln_collect') and a.soln_collect:
            first_key = list(a.soln_collect.keys())[0]
            first_soln = a.soln_collect[first_key]
            net_cols = [col for col in first_soln.columns if 'Net' in col]
            print(f"  ✅ soln_collect: {len(a.soln_collect)} 个工况, Net列: {len(net_cols)}")
        
        print("3. 测试下游分析兼容性...")
        
        # 模拟下游分析代码的调用
        if hasattr(a, 'integral_ROP') and a.integral_ROP:
            # 测试数据访问
            temp_keys = list(a.integral_ROP.keys())
            print(f"  ✅ 可访问温度点: {temp_keys}")
            
            # 测试数据格式
            first_temp = temp_keys[0]
            rop_data = a.integral_ROP[first_temp]
            print(f"  ✅ ROP数据格式正确: {rop_data.shape}")
            
            # 测试组分访问
            if hasattr(a, 'rop_species_output') and a.rop_species_output:
                print(f"  ✅ 组分输出: {len(a.rop_species_output)} 个组分")
        
        print("\n" + "=" * 60)
        print("✅ 集成测试通过!")
        print("优化版本与主应用完全兼容")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_performance_comparison():
    """简单的性能对比测试"""
    print("\n开始性能对比测试...")
    
    gas_file = "TEST/test1_gas.out"
    chemkin_file = "TEST/test1_results.xlsm"
    
    try:
        import time
        
        # 测试优化版本
        print("测试优化版本性能...")
        start_time = time.time()
        
        a = optimized_solution(gas_file, test=True)
        a.process_gas_out()
        
        b = optimized_ckfile(chemkin_file, use_optimized=True)
        b.load_chemkin_file()
        b.combine_sheets_optimized(a, progress_bar=False)
        
        optimized_time = time.time() - start_time
        print(f"优化版本耗时: {optimized_time:.2f} 秒")
        
        print("\n性能测试完成!")
        
    except Exception as e:
        print(f"❌ 性能测试失败: {str(e)}")


if __name__ == "__main__":
    success = test_integration()
    if success:
        test_performance_comparison()
    else:
        print("❌ 集成测试失败，跳过性能测试")
