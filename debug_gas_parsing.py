#!/usr/bin/env python3
"""
调试gas.out文件解析问题
"""

def debug_gas_parsing():
    """调试gas.out文件解析"""
    gas_file = "TEST/test1_gas.out"
    
    try:
        with open(gas_file, 'r') as f:
            gas_out = f.read()
        
        gas_out_lines = gas_out.splitlines()
        
        print(f"文件总行数: {len(gas_out_lines)}")
        
        # 查找CONSIDERED行
        considered_lines = []
        for i, line in enumerate(gas_out_lines):
            if 'CONSIDERED' in line:
                considered_lines.append((i, line))
        
        print(f"\n包含'CONSIDERED'的行:")
        for i, line in considered_lines:
            print(f"  行{i}: {line}")
        
        # 查找species相关的行
        species_flag = False
        species_info = []
        for line in gas_out_lines:
            if species_flag == True and '(k = A T**b exp(-E/RT))' in line:
                species_flag = False
            if line.startswith(' CONSIDERED '):
                species_flag = True
            if species_flag == True:
                species_info.append(line)
        
        print(f"\n提取的species_info行数: {len(species_info)}")
        if species_info:
            print("前5行:")
            for i, line in enumerate(species_info[:5]):
                print(f"  {i}: {line}")

            # 测试元素解析
            print(f"\n测试元素解析:")
            first_line = species_info[0]
            print(f"第一行: {first_line}")
            split_result = first_line.split()
            print(f"分割结果: {split_result}")
            print(f"分割结果长度: {len(split_result)}")
            if len(split_result) > 6:
                element_list = split_result[6:]
                print(f"元素列表: {element_list}")
            else:
                print("❌ 分割结果长度不足6")
        else:
            print("❌ 没有找到species_info")
            
            # 查找可能的起始行
            print("\n查找可能的起始行:")
            for i, line in enumerate(gas_out_lines):
                if 'CONSIDERED' in line or 'SPECIES' in line:
                    print(f"  行{i}: {line}")
                    if i < len(gas_out_lines) - 1:
                        print(f"  行{i+1}: {gas_out_lines[i+1]}")
                    print()
        
    except Exception as e:
        print(f"❌ 调试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    debug_gas_parsing()
