"""
Critical fixes needed for chemkin_solution_optimized.py
These modifications ensure feature parity with the original implementation.
"""

# ============================================================================
# FIX 1: Add missing _extract_endpoints_from_solution_optimized method
# ============================================================================

def _extract_endpoints_from_solution_optimized(self, gas_out_file, progress_callback=None):
    """
    Optimized version of endpoint extraction from solution sheets
    This method is CRITICAL for Scenario B (files without end_point_sheets)
    """
    if progress_callback:
        progress_callback(0, 100, "开始从solution sheets提取端点数据")
    print("开始从solution sheets提取端点数据...")
    
    # Use batch-read data if available, otherwise read individually
    if hasattr(self, '_current_batch_data') and self._current_batch_data:
        all_sheets_data = self._current_batch_data
    else:
        # Fallback to individual reading
        all_sheets_data = {}
        for sheet in self.soln_sheets:
            all_sheets_data[sheet] = self.xlsx.parse(sheet, index_col=0)
    
    # Group sheets by condition
    condition_sheets = {}
    for sheet in self.soln_sheets:
        if '#' in sheet:
            condition_id = sheet.split('#')[-1].split('_')[0]
        else:
            condition_id = '1'
        
        if condition_id not in condition_sheets:
            condition_sheets[condition_id] = []
        condition_sheets[condition_id].append(sheet)
    
    # Process each condition
    first_point_data = {}
    last_point_data = {}
    
    for i, (condition_id, sheets) in enumerate(condition_sheets.items()):
        if progress_callback:
            progress = 10 + (i / len(condition_sheets)) * 60
            progress_callback(progress, 100, f"处理工况 {condition_id}")
        
        # Merge data for this condition
        condition_data = []
        for sheet in sheets:
            if sheet in all_sheets_data:
                condition_data.append(all_sheets_data[sheet])
        
        if condition_data:
            merged_data = pd.concat(condition_data, axis=1) if len(condition_data) > 1 else condition_data[0]
            
            # Extract endpoints
            first_point_data[condition_id] = merged_data.iloc[0]
            last_point_data[condition_id] = merged_data.iloc[-1]
    
    # Convert to DataFrame format (similar to end_point sheets structure)
    if last_point_data:
        # Create DataFrames
        last_df_list = []
        for condition_id, data in last_point_data.items():
            last_row = pd.DataFrame([data.values], columns=data.index, index=[condition_id])
            last_df_list.append(last_row)
        
        last_points_df = pd.concat(last_df_list, axis=0).sort_index()
        gas_out_file.last_points = last_points_df
        
        # Process mole fractions
        mole_cols = [col for col in last_points_df.columns if 'Mole_fraction_' in col]
        if mole_cols:
            # Extract mole fraction data properly
            mole_data = {}
            for condition_id in last_points_df.index:
                condition_mole_cols = [col for col in mole_cols if f'_Run#{condition_id}_' in col]
                for col in condition_mole_cols:
                    species = col.split('Mole_fraction_')[1].split(f'_Run#{condition_id}_')[0]
                    if species not in mole_data:
                        mole_data[species] = {}
                    mole_data[species][condition_id] = last_points_df.loc[condition_id, col]
            
            if mole_data:
                mole_fractions = pd.DataFrame(mole_data).T
                # CRITICAL: Set proper index for compatibility
                mole_fractions.index = self.variables if len(self.variables) == len(mole_fractions.columns) else mole_fractions.index
                gas_out_file.mole_fractions = mole_fractions
                
                # CRITICAL: Calculate mole_fractions_max correctly
                gas_out_file.mole_fractions_max = gas_out_file.mole_fractions.max().to_frame().T
                gas_out_file.mole_fractions_max.index = ['max_values']
                
                # Calculate possible_reactants
                gas_out_file.possible_reactants = gas_out_file.mole_fractions_max.T.nlargest(
                    8, 'max_values').index.tolist()
        
        # Set availability flags
        self.mole_fraction_exist = len(mole_cols) > 0
        rate_cols = [col for col in last_points_df.columns if 'Net_rxn_rate_' in col]
        self.net_reaction_rate_exist = len(rate_cols) > 0
        
        if progress_callback:
            progress_callback(100, 100, "端点数据提取完成!")
        print("端点数据提取完成!")

# ============================================================================
# FIX 2: Modify combine_sheets_optimized to handle Scenario B
# ============================================================================

def combine_sheets_optimized_fixed(self, gas_out_file, progress_bar=False, mole_only=False):
    """
    Fixed version of combine_sheets_optimized that handles both scenarios
    """
    if progress_bar:
        progress_bar1 = st.progress(0, text='正在合并Sheets')
        progress_bar2 = st.progress(0, text='正在积分ROP值')
        progress_bar3 = st.progress(0, text='正在处理敏感性数据')
    else:
        progress_bar1 = None
        progress_bar2 = None
        progress_bar3 = None
    
    print("正在执行优化版本的combine_sheets...")
    
    # CRITICAL FIX: Handle Scenario B (without end_point_sheets)
    if hasattr(self, 'need_extract_endpoints') and self.need_extract_endpoints:
        print("检测到需要从solution sheets提取端点数据 (Scenario B)")
        
        # Create progress callback for endpoint extraction
        def endpoint_progress_callback(step, total, message):
            if progress_bar1:
                progress = int((step / total) * 100)
                progress_bar1.progress(progress / 100, text=f"🔄 {message}")
        
        self._extract_endpoints_from_solution_optimized(gas_out_file, endpoint_progress_callback)
    
    # Batch read sheets (optimization)
    print("优化1: 批量预读取Excel数据...")
    all_sheets_data = self._batch_read_sheets(progress_bar1)
    
    # Store batch data for endpoint extraction if needed
    self._current_batch_data = all_sheets_data
    
    # Process mole fractions data
    if self.mole_fraction_exist:
        if len(self.end_point_sheets) > 0:
            # Scenario A: with end_point_sheets
            print("处理Scenario A: 使用end_point_sheets")
            self._process_mole_fractions_optimized_fixed(all_sheets_data, gas_out_file)
        else:
            # Scenario B: without end_point_sheets (data already extracted above)
            print("处理Scenario B: 使用从solution sheets提取的数据")
            # Data already processed in _extract_endpoints_from_solution_optimized
            pass
    
    # Continue with rest of processing...
    if len(self.soln_sheets) > 0:
        self._process_solution_data_optimized(all_sheets_data, gas_out_file, progress_bar1)
    
    if self.sensitivity_exist and len(self.sensitivity_sheets) > 0:
        self._process_sensitivity_data_optimized(all_sheets_data, gas_out_file, progress_bar3)
    
    if self.net_reaction_rate_exist:
        self._process_reaction_rates_optimized(all_sheets_data, gas_out_file, progress_bar1, progress_bar2)
    elif self.ROP_exist:
        self._process_rop_data_optimized(all_sheets_data, gas_out_file, progress_bar2)
    
    # Clean up batch data
    if hasattr(self, '_current_batch_data'):
        delattr(self, '_current_batch_data')
    
    # Data integrity validation
    print("\n📊 数据完整性验证:")
    self._validate_data_integrity(gas_out_file)
    
    print(f"\n✅ 优化版本数据处理完成!")

# ============================================================================
# FIX 3: Fix _process_mole_fractions_optimized for proper data structure
# ============================================================================

def _process_mole_fractions_optimized_fixed(self, all_sheets_data, gas_out_file):
    """
    Fixed version that ensures proper data structure consistency
    """
    print("处理摩尔分数数据...")
    
    # Process end_point sheets
    mole_fraction_dfs = []
    for sheet in self.end_point_sheets:
        if sheet in all_sheets_data:
            sheet_data = all_sheets_data[sheet]
            mole_cols = sheet_data.columns[sheet_data.columns.str.contains(' Mole_fraction_')]
            mole_fraction_dfs.append(sheet_data[mole_cols])
    
    if mole_fraction_dfs:
        # Combine all data
        combined_mole_fractions = pd.concat(mole_fraction_dfs, axis=1)
        gas_out_file.mole_fractions = combined_mole_fractions.copy()
        gas_out_file.mole_fractions.columns = (
            gas_out_file.mole_fractions.columns
            .str.replace(' Mole_fraction_', '')
            .str.replace('_end_point_()', '', regex=False)
        )
        
        # CRITICAL FIX: Set proper index for compatibility
        gas_out_file.mole_fractions.index = self.variables
        
        # CRITICAL FIX: Calculate mole_fractions_max from mole_fractions, not max_sheets
        gas_out_file.mole_fractions_max = gas_out_file.mole_fractions.max().to_frame().T
        gas_out_file.mole_fractions_max.index = ['max_values']
        
        # Calculate possible_reactants
        gas_out_file.possible_reactants = gas_out_file.mole_fractions_max.T.nlargest(
            8, 'max_values').index.tolist()
        
        print(f"✅ 摩尔分数数据处理完成: {gas_out_file.mole_fractions.shape}")

# ============================================================================
# FIX 4: Add missing utility method
# ============================================================================

def get_available_gradient_variables(self):
    """Get available gradient variables list"""
    print("=" * 60)
    print("检测可用的梯度变量...")
    print("=" * 60)
    
    if len(self.soln_sheets) > 0:
        first_sheet = self.xlsx.parse(self.soln_sheets[0], index_col=0)
        columns = first_sheet.columns.tolist()
        print(f"从 {self.soln_sheets[0]} 读取到 {len(columns)} 个列")
        print("前10个列名:")
        for i, col in enumerate(columns[:10]):
            print(f"  {i+1:2d}. {col}")
        
        # Filter available gradient variables
        gradient_vars = []
        for col in columns:
            if any(keyword in col for keyword in ['Temperature', 'Pressure', 'Mole_fraction']):
                gradient_vars.append(col)
        
        print(f"\n可用的梯度变量 ({len(gradient_vars)} 个):")
        for var in gradient_vars[:20]:  # Show first 20
            print(f"  - {var}")
        
        return gradient_vars
    else:
        print("❌ 没有可用的solution sheets")
        return []
