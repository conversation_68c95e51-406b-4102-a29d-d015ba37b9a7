"""
PyInstaller打包脚本
用于将Streamlit应用打包成可执行文件
"""
import os
import sys
import shutil
import subprocess
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent
MAIN_SCRIPT = PROJECT_ROOT / "主程序.py"
DIST_DIR = PROJECT_ROOT / "dist"
BUILD_DIR = PROJECT_ROOT / "build"

# PyInstaller配置
PYINSTALLER_ARGS = [
    "--name=ROP_Analyzer",
    "--onefile",
    "--windowed",
    "--icon=icon.ico",  # 如果有图标文件
    "--add-data=pages;pages",
    "--add-data=TEST;TEST", 
    "--add-data=教程.docx;.",
    "--hidden-import=streamlit",
    "--hidden-import=plotly",
    "--hidden-import=cantera",
    "--hidden-import=scipy",
    "--hidden-import=lmfit",
    "--hidden-import=openpyxl",
    "--collect-submodules=streamlit",
    "--collect-submodules=plotly",
    str(MAIN_SCRIPT)
]

def clean_build_dirs():
    """清理构建目录"""
    print("清理构建目录...")
    if DIST_DIR.exists():
        shutil.rmtree(DIST_DIR)
    if BUILD_DIR.exists():
        shutil.rmtree(BUILD_DIR)
    
    # 清理spec文件
    spec_files = list(PROJECT_ROOT.glob("*.spec"))
    for spec_file in spec_files:
        spec_file.unlink()

def build_executable():
    """构建可执行文件"""
    print("开始构建可执行文件...")
    print(f"主脚本: {MAIN_SCRIPT}")
    
    try:
        # 运行PyInstaller
        cmd = ["pyinstaller"] + PYINSTALLER_ARGS
        print(f"执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, cwd=PROJECT_ROOT, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("构建成功！")
            print(f"可执行文件位置: {DIST_DIR}")
        else:
            print("构建失败！")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"构建过程中出现错误: {e}")
        return False
    
    return True

def create_launcher_script():
    """创建启动脚本"""
    launcher_content = '''@echo off
echo 正在启动ROP分析工具箱...
echo 请稍等，程序启动后会自动打开浏览器窗口
start "" ROP_Analyzer.exe
echo 如果浏览器没有自动打开，请手动打开 http://localhost:8501
pause
'''
    
    launcher_path = DIST_DIR / "启动ROP分析工具.bat"
    with open(launcher_path, 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print(f"创建启动脚本: {launcher_path}")

def copy_additional_files():
    """复制额外的文件"""
    additional_files = [
        "README.md",
        "OPTIMIZATION_PLAN.md",
        "requirements.txt"
    ]
    
    for file_name in additional_files:
        src_file = PROJECT_ROOT / file_name
        if src_file.exists():
            dst_file = DIST_DIR / file_name
            shutil.copy2(src_file, dst_file)
            print(f"复制文件: {file_name}")

def main():
    """主函数"""
    print("=== ROP分析工具箱 EXE打包工具 ===")
    print(f"项目根目录: {PROJECT_ROOT}")
    
    # 检查主脚本是否存在
    if not MAIN_SCRIPT.exists():
        print(f"错误: 找不到主脚本 {MAIN_SCRIPT}")
        return
    
    # 清理构建目录
    clean_build_dirs()
    
    # 构建可执行文件
    if build_executable():
        # 创建启动脚本
        create_launcher_script()
        
        # 复制额外文件
        copy_additional_files()
        
        print("\n=== 打包完成 ===")
        print(f"输出目录: {DIST_DIR}")
        print("可执行文件: ROP_Analyzer.exe")
        print("启动脚本: 启动ROP分析工具.bat")
    else:
        print("\n=== 打包失败 ===")
        print("请检查错误信息并重试")

if __name__ == "__main__":
    main()