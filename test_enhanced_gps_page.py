#!/usr/bin/env python3
"""
测试增强的GPS分析页面功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from solution0606 import solution as original_solution, ckfile as original_ckfile
from src.gps_integration import IntegratedGPSAnalyzer


def test_enhanced_gps_functionality():
    """测试增强的GPS功能"""
    print("=" * 80)
    print("测试增强的GPS分析页面功能")
    print("=" * 80)
    
    # 使用GPS测试文件
    gas_file = "TEST/test_gps.out"
    chemkin_file = "TEST/test_gps_results.xlsm"
    
    try:
        print("1. 加载测试数据...")
        
        # 加载gas数据
        gas_obj = original_solution(gas_file, test=True)
        gas_obj.process_gas_out()
        
        # 加载chemkin数据
        chemkin_obj = original_ckfile(chemkin_file)
        chemkin_obj.load_chemkin_file()
        chemkin_obj.combine_sheets(gas_obj, progress_bar=False)
        
        print("2. 验证数据完整性...")
        
        # 检查必要的数据
        data_checks = {
            'mole_fractions': hasattr(gas_obj, 'mole_fractions') and gas_obj.mole_fractions is not None,
            'net_reaction_rate': chemkin_obj.net_reaction_rate_exist,
            'integral_ROP': hasattr(gas_obj, 'integral_ROP') and gas_obj.integral_ROP is not None,
            'species_element': hasattr(gas_obj, 'species_element') and gas_obj.species_element is not None,
            'species_MW': hasattr(gas_obj, 'species_MW') and gas_obj.species_MW is not None
        }
        
        print("   数据完整性检查:")
        for check_name, result in data_checks.items():
            status = "✅" if result else "❌"
            print(f"     {status} {check_name}: {result}")
        
        if not all(data_checks.values()):
            print("⚠️ 部分数据缺失，但继续测试...")
        
        print("3. 测试GPS分析器...")
        gps_analyzer = IntegratedGPSAnalyzer()
        
        # 测试数据加载
        success = gps_analyzer.load_project_data(
            gas_obj=gas_obj,
            condition_key=None,
            use_end_point=False
        )
        
        if not success:
            print("❌ 数据加载失败")
            return False
        
        print("   ✅ 数据加载成功")
        
        # 测试GPS分析
        species_names = gps_analyzer.current_data['species_names']
        fuel_species = list(gps_analyzer.current_data['fuel_composition'].keys())
        
        if not fuel_species:
            print("❌ 未找到燃料组分")
            return False
        
        source = fuel_species[0]
        target = 'CO2' if 'CO2' in species_names else species_names[-1]
        
        print(f"   分析路径: {source} → {target}")
        
        gps_results = gps_analyzer.run_gps_analysis(
            source=source,
            target=target,
            K=1,
            alpha=0.1,
            beta=0.5,
            traced_element='C'
        )
        
        print("   ✅ GPS分析完成")
        
        # 测试自动GPSA分析
        print("4. 测试自动GPSA分析...")
        pathway_details = gps_analyzer.get_pathway_details()
        gpsa_results = {}
        
        for pathway in pathway_details:
            try:
                gpsa_result = gps_analyzer.run_gpsa_analysis(pathway['name'])
                gpsa_results[pathway['name']] = gpsa_result
                print(f"   ✅ 路径 {pathway['name'][:30]}... GPSA分析完成")
            except Exception as e:
                print(f"   ⚠️ 路径 {pathway['name'][:30]}... GPSA分析失败: {str(e)}")
        
        print("5. 测试路径命名系统...")
        pathway_codes = {}
        for i, pathway in enumerate(pathway_details):
            pathway_code = f"P{i+1}"
            pathway_codes[pathway['name']] = pathway_code
            source_species = pathway['species_sequence'][0] if pathway['species_sequence'] else "Unknown"
            target_species = pathway['species_sequence'][-1] if len(pathway['species_sequence']) > 1 else "Unknown"
            print(f"   {pathway_code}: {source_species} → {target_species} ({pathway['length']} 步)")
        
        print("6. 测试数据表生成...")
        
        # 测试GPSA参数表
        if gpsa_results:
            print("   ✅ GPSA参数表数据准备完成")
            for pathway_name, gpsa_data in gpsa_results.items():
                pathway_code = pathway_codes.get(pathway_name, "Unknown")
                d_gp = gpsa_data.get('D_GP', [0])[0] if gpsa_data.get('D_GP') else 0
                r_gp = gpsa_data.get('R_GP', [0])[0] if gpsa_data.get('R_GP') else 0
                print(f"     {pathway_code}: D_GP={d_gp:.6f}, R_GP={r_gp:.2e}")
        
        # 测试组分选择详情
        species_details = gps_analyzer.get_species_selection_details()
        print(f"   ✅ 组分选择详情:")
        print(f"     枢纽组分: {len(species_details['by_alpha'])} 个")
        print(f"     路径组分: {len(species_details['by_K'])} 个")
        print(f"     反应组分: {len(species_details['by_beta'])} 个")
        
        print("7. 测试结果导出...")
        try:
            output_dir = gps_analyzer.export_results("test_enhanced_gps_output")
            print(f"   ✅ 结果导出成功: {output_dir}")
        except Exception as e:
            print(f"   ⚠️ 结果导出失败: {str(e)}")
        
        print("\n" + "=" * 80)
        print("🎉 增强的GPS功能测试完成!")
        print("所有核心功能正常工作")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_enhanced_gps_functionality()
    if success:
        print("\n✅ 增强的GPS分析页面已准备就绪!")
    else:
        print("\n❌ 测试发现问题，需要进一步调试")
