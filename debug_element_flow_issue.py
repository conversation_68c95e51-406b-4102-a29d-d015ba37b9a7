#!/usr/bin/env python3
"""
调试元素流向分析的NaN问题
使用test_gas.out和test_gps_results.xlsm
"""

import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def debug_element_flow_nan():
    """调试元素流向分析的NaN问题"""
    print("=" * 80)
    print("调试元素流向分析NaN问题")
    print("=" * 80)
    
    try:
        # 导入优化版本
        from src.optimized import compatibility_wrapper as opt_sl
        
        # 使用指定的测试文件
        gas_file = "TEST/test_gps.out"
        chemkin_file = "TEST/test_gps_results.xlsm"
        
        print("1. 加载数据...")
        
        # 创建对象
        gas_obj = opt_sl.solution(gas_file, test=True)
        gas_obj.process_gas_out(False)  # 不显示进度条
        
        chemkin_obj = opt_sl.ckfile(chemkin_file)
        chemkin_obj.load_chemkin_file()
        chemkin_obj.combine_sheets(gas_obj, progress_bar=False)
        
        print("2. 检查数据结构...")
        
        print(f"   gas_obj.species_element shape: {gas_obj.species_element.shape}")
        print(f"   gas_obj.species_element columns: {list(gas_obj.species_element.columns)}")
        
        print(f"   gas_obj.mole_fractions shape: {gas_obj.mole_fractions.shape}")
        print(f"   gas_obj.mole_fractions index: {list(gas_obj.mole_fractions.index)}")
        
        if hasattr(gas_obj, 'mole_fractions_max'):
            print(f"   gas_obj.mole_fractions_max shape: {gas_obj.mole_fractions_max.shape}")
            print(f"   gas_obj.mole_fractions_max index: {list(gas_obj.mole_fractions_max.index)}")
        else:
            print("   ❌ mole_fractions_max 不存在")
        
        print(f"   chemkin_obj.variables: {chemkin_obj.variables}")
        
        print("3. 手动调试ele_ini过程...")
        
        # 测试参数
        test_reactants = ['CH4']
        
        print(f"   使用反应物: {test_reactants}")
        
        # 手动执行ele_ini的逻辑
        print("   步骤1: 创建初始DataFrame...")
        elements_initial = pd.DataFrame(
            index=gas_obj.species_element.columns, 
            columns=chemkin_obj.variables, 
            data=0
        )
        print(f"   初始 elements_initial shape: {elements_initial.shape}")
        print(f"   初始 elements_initial index: {list(elements_initial.index)}")
        print(f"   初始 elements_initial columns: {list(elements_initial.columns)}")
        
        for reactant in test_reactants:
            print(f"\n   步骤2: 处理反应物 {reactant}...")
            
            # 检查反应物是否存在
            if reactant not in gas_obj.species_element.index:
                print(f"   ❌ {reactant} 不在 species_element.index 中")
                continue
                
            if reactant not in gas_obj.mole_fractions_max.columns:
                print(f"   ❌ {reactant} 不在 mole_fractions_max.columns 中")
                continue
            
            # 获取反应物的元素组成
            reactant_elements = gas_obj.species_element.loc[reactant, :]
            print(f"   {reactant} 元素组成: {reactant_elements.tolist()}")
            
            # 获取反应物的摩尔分数
            reactant_mole_fractions = gas_obj.mole_fractions_max.loc[:, reactant]
            print(f"   {reactant} 摩尔分数 shape: {reactant_mole_fractions.shape}")
            print(f"   {reactant} 摩尔分数 index: {list(reactant_mole_fractions.index)}")
            print(f"   {reactant} 摩尔分数值: {reactant_mole_fractions.tolist()}")
            
            # 问题分析：检查索引对齐
            print(f"\n   索引对齐检查:")
            print(f"   elements_initial.columns: {list(elements_initial.columns)}")
            print(f"   reactant_mole_fractions.index: {list(reactant_mole_fractions.index)}")
            print(f"   索引是否匹配: {list(elements_initial.columns) == list(reactant_mole_fractions.index)}")
            
            # 尝试修复：重新索引
            print(f"\n   尝试修复索引对齐...")
            
            # 方法1：使用reindex对齐索引
            aligned_mole_fractions = reactant_mole_fractions.reindex(elements_initial.columns)
            print(f"   对齐后摩尔分数: {aligned_mole_fractions.tolist()}")
            print(f"   对齐后是否有NaN: {aligned_mole_fractions.isna().any()}")
            
            if aligned_mole_fractions.isna().any():
                print("   ❌ 索引对齐后出现NaN，尝试其他方法...")
                
                # 方法2：手动映射
                print("   尝试手动映射...")
                manual_mapping = {}
                for var in elements_initial.columns:
                    if var in reactant_mole_fractions.index:
                        manual_mapping[var] = reactant_mole_fractions.loc[var]
                    else:
                        print(f"   警告: 变量 {var} 不在摩尔分数数据中")
                        manual_mapping[var] = 0.0
                
                aligned_mole_fractions = pd.Series(manual_mapping, index=elements_initial.columns)
                print(f"   手动映射后摩尔分数: {aligned_mole_fractions.tolist()}")
            
            # 计算元素贡献
            print(f"\n   计算元素贡献...")
            element_contribution = pd.DataFrame(index=elements_initial.index, columns=elements_initial.columns)
            
            for element in elements_initial.index:
                element_content = reactant_elements.loc[element]
                element_contribution.loc[element, :] = element_content * aligned_mole_fractions
            
            print(f"   元素贡献 shape: {element_contribution.shape}")
            print(f"   元素贡献 (C): {element_contribution.loc['C', :].tolist() if 'C' in element_contribution.index else 'N/A'}")
            
            # 累加
            print(f"\n   累加到elements_initial...")
            elements_initial = elements_initial + element_contribution
            print(f"   累加后 elements_initial (C): {elements_initial.loc['C', :].tolist() if 'C' in elements_initial.index else 'N/A'}")
        
        print("\n4. 测试修复后的完整流程...")
        
        # 使用修复后的逻辑测试完整流程
        gas_obj_test = opt_sl.solution(gas_file, test=True)
        gas_obj_test.process_gas_out(False)
        
        chemkin_obj_test = opt_sl.ckfile(chemkin_file)
        chemkin_obj_test.load_chemkin_file()
        chemkin_obj_test.combine_sheets(gas_obj_test, progress_bar=False)
        
        # 手动实现修复后的ele_ini
        def fixed_ele_ini(gas_obj, chemkin_obj, reactants):
            elements_initial = pd.DataFrame(
                index=gas_obj.species_element.columns, 
                columns=chemkin_obj.variables, 
                data=0.0
            )
            
            for reactant in reactants:
                if reactant not in gas_obj.species_element.index:
                    continue
                if reactant not in gas_obj.mole_fractions_max.columns:
                    continue
                
                reactant_elements = gas_obj.species_element.loc[reactant, :]
                reactant_mole_fractions = gas_obj.mole_fractions_max.loc[:, reactant]
                
                # 对齐索引
                aligned_mole_fractions = reactant_mole_fractions.reindex(elements_initial.columns, fill_value=0.0)
                
                # 计算元素贡献
                for element in elements_initial.index:
                    element_content = reactant_elements.loc[element]
                    elements_initial.loc[element, :] += element_content * aligned_mole_fractions
            
            # 过滤和转置
            elements_initial = elements_initial.loc[elements_initial.max(axis=1) != 0, :].T
            return elements_initial
        
        fixed_elements_initial = fixed_ele_ini(gas_obj_test, chemkin_obj_test, ['CH4'])
        print(f"   修复后 elements_initial shape: {fixed_elements_initial.shape}")
        print(f"   修复后 elements_initial (C列): {fixed_elements_initial.loc[:, 'C'].tolist() if 'C' in fixed_elements_initial.columns else 'N/A'}")
        print(f"   修复后是否有NaN: {fixed_elements_initial.isna().any().any()}")
        
        print("\n✅ 调试完成")
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🚀 开始调试元素流向分析NaN问题")
    
    success = debug_element_flow_nan()
    
    if success:
        print("\n🎉 调试完成！")
        print("\n发现的问题:")
        print("1. elements_initial和mole_fractions_max的索引不匹配")
        print("2. pandas在相加时产生NaN值")
        print("\n解决方案:")
        print("1. 使用reindex对齐索引")
        print("2. 使用fill_value=0.0处理缺失值")
        return True
    else:
        print("\n❌ 调试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    main()
