#!/usr/bin/env python3
"""
测试GPS页面改进功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_theme_improvements():
    """测试主题改进"""
    print("=" * 80)
    print("测试主题对比度改进")
    print("=" * 80)
    
    try:
        # 测试主题管理器
        from src.theme_manager import get_light_theme_css, get_dark_theme_css
        
        print("1. 测试亮色主题CSS...")
        light_css = get_light_theme_css()
        
        # 检查关键的对比度修复
        contrast_fixes = [
            "stSelectbox > div > div > div",
            "color: var(--text-color) !important",
            "background-color: var(--background-color) !important",
            "div[data-testid=\"stSelectbox\"]",
            "ul[role=\"listbox\"] li"
        ]
        
        for fix in contrast_fixes:
            if fix in light_css:
                print(f"   ✅ 找到对比度修复: {fix}")
            else:
                print(f"   ❌ 缺少对比度修复: {fix}")
        
        print("2. 测试暗色主题CSS...")
        dark_css = get_dark_theme_css()
        
        for fix in contrast_fixes:
            if fix in dark_css:
                print(f"   ✅ 找到对比度修复: {fix}")
            else:
                print(f"   ❌ 缺少对比度修复: {fix}")
        
        print("3. 检查警告卡片样式...")
        if "warning-card" in light_css and "warning-card" in dark_css:
            print("   ✅ 警告卡片样式已添加")
        else:
            print("   ❌ 警告卡片样式缺失")
        
        print("✅ 主题改进测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 主题测试失败: {e}")
        return False


def test_gps_page_structure():
    """测试GPS页面结构"""
    print("\n" + "=" * 80)
    print("测试GPS页面结构改进")
    print("=" * 80)
    
    try:
        # 读取GPS页面文件
        gps_page_path = "pages/8GPS全局路径分析.py"
        
        with open(gps_page_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("1. 检查标签页结构...")
        
        # 检查标签页创建
        if "st.tabs([" in content:
            print("   ✅ 找到标签页创建")
        else:
            print("   ❌ 缺少标签页创建")
        
        # 检查四个标签页
        expected_tabs = [
            "📈 GPSA参数比较",
            "📊 参数趋势分析", 
            "🎯 组分选择分析",
            "🛤️ 路径详情"
        ]
        
        for tab in expected_tabs:
            if tab in content:
                print(f"   ✅ 找到标签页: {tab}")
            else:
                print(f"   ❌ 缺少标签页: {tab}")
        
        print("2. 检查重复内容移除...")
        
        # 检查路径命名系统是否被移除
        if "路径命名系统" not in content or content.count("路径命名系统") <= 1:
            print("   ✅ 路径命名系统重复内容已移除")
        else:
            print("   ❌ 路径命名系统仍有重复内容")
        
        print("3. 检查数据验证...")
        
        # 检查数据验证
        validation_checks = [
            "mole_fractions",
            "net_reaction_rate_exist", 
            "has_enthalpy_data",
            "warning-card"
        ]
        
        for check in validation_checks:
            if check in content:
                print(f"   ✅ 找到数据验证: {check}")
            else:
                print(f"   ❌ 缺少数据验证: {check}")
        
        print("4. 检查自动GPSA分析...")
        
        if "自动运行GPSA分析" in content:
            print("   ✅ 找到自动GPSA分析")
        else:
            print("   ❌ 缺少自动GPSA分析")
        
        print("5. 检查下载功能...")
        
        download_features = [
            "download_button",
            "to_csv",
            "encoding='utf-8-sig'"
        ]
        
        for feature in download_features:
            if feature in content:
                print(f"   ✅ 找到下载功能: {feature}")
            else:
                print(f"   ❌ 缺少下载功能: {feature}")
        
        print("✅ GPS页面结构测试完成")
        return True
        
    except Exception as e:
        print(f"❌ GPS页面结构测试失败: {e}")
        return False


def test_file_integrity():
    """测试文件完整性"""
    print("\n" + "=" * 80)
    print("测试文件完整性")
    print("=" * 80)
    
    try:
        # 检查关键文件是否存在
        key_files = [
            "pages/8GPS全局路径分析.py",
            "src/theme_manager.py",
            "src/gps_integration/__init__.py",
            "src/gps_integration/gps_analyzer.py",
            "src/gps_integration/data_converter.py"
        ]
        
        for file_path in key_files:
            if os.path.exists(file_path):
                print(f"   ✅ 文件存在: {file_path}")
            else:
                print(f"   ❌ 文件缺失: {file_path}")
        
        # 检查GPS页面语法
        print("\n检查GPS页面语法...")
        try:
            with open("pages/8GPS全局路径分析.py", 'r', encoding='utf-8') as f:
                code = f.read()
            
            # 简单的语法检查
            compile(code, "pages/8GPS全局路径分析.py", 'exec')
            print("   ✅ GPS页面语法正确")
            
        except SyntaxError as e:
            print(f"   ❌ GPS页面语法错误: {e}")
            return False
        
        # 检查主题管理器语法
        print("检查主题管理器语法...")
        try:
            with open("src/theme_manager.py", 'r', encoding='utf-8') as f:
                code = f.read()
            
            compile(code, "src/theme_manager.py", 'exec')
            print("   ✅ 主题管理器语法正确")
            
        except SyntaxError as e:
            print(f"   ❌ 主题管理器语法错误: {e}")
            return False
        
        print("✅ 文件完整性测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 文件完整性测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始GPS页面改进测试")
    
    results = []
    
    # 运行各项测试
    results.append(test_theme_improvements())
    results.append(test_gps_page_structure())
    results.append(test_file_integrity())
    
    # 总结结果
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！GPS页面改进成功完成")
        print("\n主要改进:")
        print("✅ 修复了下拉框/选择框的对比度问题")
        print("✅ 移除了重复的路径命名系统内容")
        print("✅ 重新组织内容为4个标签页")
        print("✅ 增强了数据验证和用户警告")
        print("✅ 实现了自动GPSA分析")
        print("✅ 改进了下载功能")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ GPS页面改进已准备就绪!")
    else:
        print("\n❌ 发现问题，需要进一步调试")
