#!/usr/bin/env python3
"""
测试GPS集成功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from solution0606 import solution as original_solution, ckfile as original_ckfile
from src.gps_integration import IntegratedGPSAnalyzer


def test_data_conversion():
    """测试数据转换功能"""
    print("=" * 80)
    print("测试GPS数据转换功能")
    print("=" * 80)
    
    # 使用GPS测试文件
    gas_file = "TEST/test_gps.out"
    chemkin_file = "TEST/test_gps_results.xlsm"
    
    try:
        print("1. 加载测试数据...")
        
        # 加载gas数据
        gas_obj = original_solution(gas_file, test=True)
        gas_obj.process_gas_out()
        
        # 加载chemkin数据
        chemkin_obj = original_ckfile(chemkin_file)
        chemkin_obj.load_chemkin_file()
        chemkin_obj.combine_sheets(gas_obj, progress_bar=False)
        
        print("2. 初始化GPS分析器...")
        gps_analyzer = IntegratedGPSAnalyzer()
        
        print("3. 测试数据转换...")
        success = gps_analyzer.load_project_data(
            gas_obj=gas_obj,
            condition_key=None,  # 使用第一个条件
            use_end_point=False  # 使用积分数据
        )
        
        if success:
            print("✅ 数据转换成功!")
            
            # 显示转换后的数据信息
            data = gps_analyzer.current_data
            print(f"\n数据信息:")
            print(f"  组分数量: {len(data['species_names'])}")
            print(f"  反应数量: {len(data['reaction_stoichiometry'])}")
            print(f"  元素组成: {len(data['species_elemental_composition'])} 个组分")
            print(f"  燃料组分: {len(data['fuel_composition'])} 个")
            print(f"  条件: {data['condition_info']['condition_key']}")
            
            # 显示一些示例数据
            print(f"\n前5个组分: {data['species_names'][:5]}")
            print(f"燃料组分: {list(data['fuel_composition'].keys())[:5]}")
            
            # 显示元素组成示例
            print(f"\n元素组成示例:")
            for i, (species, composition) in enumerate(data['species_elemental_composition'].items()):
                if i >= 3:
                    break
                print(f"  {species}: {composition}")
            
            return gas_obj, gps_analyzer
        else:
            print("❌ 数据转换失败")
            return None, None
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None


def test_gps_analysis(gas_obj, gps_analyzer):
    """测试GPS分析功能"""
    print("\n" + "=" * 80)
    print("测试GPS分析功能")
    print("=" * 80)
    
    try:
        # 选择源和目标组分
        species_names = gps_analyzer.current_data['species_names']
        fuel_species = list(gps_analyzer.current_data['fuel_composition'].keys())
        
        if not fuel_species:
            print("❌ 未找到燃料组分")
            return False
        
        # 选择一个燃料作为源，CO2作为目标
        source = fuel_species[0]
        target = 'CO2' if 'CO2' in species_names else species_names[-1]
        
        print(f"分析路径: {source} → {target}")
        
        # 运行GPS分析
        gps_results = gps_analyzer.run_gps_analysis(
            source=source,
            target=target,
            K=1,
            alpha=0.1,
            beta=0.5,
            traced_element='C'
        )
        
        print("✅ GPS分析完成!")
        
        # 显示结果摘要
        summary = gps_analyzer.get_analysis_summary()
        print(f"\n分析结果摘要:")
        print(f"  全局路径数量: {summary['gps_summary'].get('n_global_path', 0)}")
        print(f"  枢纽组分数量: {summary['gps_summary'].get('n_hub', 0)}")
        print(f"  选择组分数量: {summary['gps_summary'].get('n_species_kept', 0)}")
        
        # 显示路径详情
        pathway_details = gps_analyzer.get_pathway_details()
        print(f"\n路径详情:")
        for i, pathway in enumerate(pathway_details):
            if i >= 3:  # 只显示前3个路径
                break
            print(f"  路径 {i+1}: {' → '.join(pathway['species_sequence'])}")
        
        # 测试GPSA分析
        if pathway_details:
            print(f"\n运行GPSA分析...")
            first_pathway = pathway_details[0]['name']
            gpsa_result = gps_analyzer.run_gpsa_analysis(first_pathway)
            print("✅ GPSA分析完成!")
        
        return True
        
    except Exception as e:
        print(f"❌ GPS分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_results_export(gps_analyzer):
    """测试结果导出功能"""
    print("\n" + "=" * 80)
    print("测试结果导出功能")
    print("=" * 80)
    
    try:
        output_dir = gps_analyzer.export_results("test_gps_output")
        print(f"✅ 结果导出成功: {output_dir}")
        
        # 检查导出的文件
        files = list(output_dir.glob("*.json"))
        print(f"导出文件: {[f.name for f in files]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 结果导出失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始GPS集成测试")
    
    # 测试数据转换
    gas_obj, gps_analyzer = test_data_conversion()
    if not gas_obj or not gps_analyzer:
        print("❌ 数据转换测试失败，终止测试")
        return
    
    # 测试GPS分析
    analysis_success = test_gps_analysis(gas_obj, gps_analyzer)
    if not analysis_success:
        print("❌ GPS分析测试失败")
        return
    
    # 测试结果导出
    export_success = test_results_export(gps_analyzer)
    
    print("\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    if analysis_success and export_success:
        print("🎉 所有测试通过!")
        print("GPS集成功能正常工作")
    else:
        print("⚠️ 部分测试失败")


if __name__ == "__main__":
    main()
