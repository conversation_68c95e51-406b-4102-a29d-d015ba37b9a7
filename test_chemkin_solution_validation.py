#!/usr/bin/env python3
"""
Comprehensive validation test for ChemkinSolution optimized vs original
Tests both Scenario A (with end_point_sheets) and Scenario B (without end_point_sheets)
"""

import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_scenario_a():
    """Test Scenario A: Files with end_point_sheets"""
    print("=" * 80)
    print("测试 Scenario A: 包含 end_point_sheets 的文件")
    print("=" * 80)
    
    try:
        from src.optimized import compatibility_wrapper as opt_sl
        
        # Test files for Scenario A
        gas_file = "TEST/test2_gas.out"
        chemkin_file = "TEST/test2_results.xlsm"
        
        print("1. 加载数据...")
        
        # Create objects
        gas_obj = opt_sl.solution(gas_file, test=True)
        gas_obj.process_gas_out(False)
        
        chemkin_obj = opt_sl.ckfile(chemkin_file)
        chemkin_obj.load_chemkin_file()
        
        # Check that end_point_sheets exist
        print(f"   end_point_sheets: {len(chemkin_obj.end_point_sheets)} 个")
        print(f"   soln_sheets: {len(chemkin_obj.soln_sheets)} 个")
        
        # Process data
        chemkin_obj.combine_sheets(gas_obj, progress_bar=False)
        
        print("2. 验证数据结构...")
        
        # Validate critical attributes
        validation_results = {}
        
        # Check mole_fractions
        if hasattr(gas_obj, 'mole_fractions') and gas_obj.mole_fractions is not None:
            validation_results['mole_fractions'] = {
                'exists': True,
                'shape': gas_obj.mole_fractions.shape,
                'index_type': type(gas_obj.mole_fractions.index),
                'index_values': list(gas_obj.mole_fractions.index)
            }
            print(f"   ✅ mole_fractions: {gas_obj.mole_fractions.shape}")
        else:
            validation_results['mole_fractions'] = {'exists': False}
            print("   ❌ mole_fractions: 缺失")
        
        # Check mole_fractions_max
        if hasattr(gas_obj, 'mole_fractions_max') and gas_obj.mole_fractions_max is not None:
            validation_results['mole_fractions_max'] = {
                'exists': True,
                'shape': gas_obj.mole_fractions_max.shape,
                'index_values': list(gas_obj.mole_fractions_max.index)
            }
            print(f"   ✅ mole_fractions_max: {gas_obj.mole_fractions_max.shape}")
        else:
            validation_results['mole_fractions_max'] = {'exists': False}
            print("   ❌ mole_fractions_max: 缺失")
        
        # Check possible_reactants
        if hasattr(gas_obj, 'possible_reactants') and gas_obj.possible_reactants is not None:
            validation_results['possible_reactants'] = {
                'exists': True,
                'count': len(gas_obj.possible_reactants),
                'values': gas_obj.possible_reactants
            }
            print(f"   ✅ possible_reactants: {len(gas_obj.possible_reactants)} 个")
        else:
            validation_results['possible_reactants'] = {'exists': False}
            print("   ❌ possible_reactants: 缺失")
        
        # Check ROP data
        if hasattr(gas_obj, 'integral_ROP') and gas_obj.integral_ROP is not None:
            validation_results['integral_ROP'] = {
                'exists': True,
                'type': type(gas_obj.integral_ROP),
                'count': len(gas_obj.integral_ROP) if isinstance(gas_obj.integral_ROP, dict) else 'N/A'
            }
            print(f"   ✅ integral_ROP: {type(gas_obj.integral_ROP)}")
        else:
            validation_results['integral_ROP'] = {'exists': False}
            print("   ❌ integral_ROP: 缺失")
        
        print("✅ Scenario A 测试完成")
        return True, validation_results
        
    except Exception as e:
        print(f"❌ Scenario A 测试失败: {str(e)}")
        return False, {}

def test_scenario_b():
    """Test Scenario B: Files without end_point_sheets"""
    print("=" * 80)
    print("测试 Scenario B: 不包含 end_point_sheets 的文件")
    print("=" * 80)
    
    try:
        from src.optimized import compatibility_wrapper as opt_sl
        
        # Test files for Scenario B
        gas_file = "TEST/test2_gas.out"
        chemkin_file = "TEST/test2_results_nosen.xlsm"
        
        print("1. 加载数据...")
        
        # Create objects
        gas_obj = opt_sl.solution(gas_file, test=True)
        gas_obj.process_gas_out(False)
        
        chemkin_obj = opt_sl.ckfile(chemkin_file)
        chemkin_obj.load_chemkin_file()
        
        # Check that end_point_sheets are missing
        print(f"   end_point_sheets: {len(chemkin_obj.end_point_sheets)} 个")
        print(f"   soln_sheets: {len(chemkin_obj.soln_sheets)} 个")
        print(f"   need_extract_endpoints: {getattr(chemkin_obj, 'need_extract_endpoints', False)}")
        
        # Process data
        chemkin_obj.combine_sheets(gas_obj, progress_bar=False)
        
        print("2. 验证数据结构...")
        
        # Validate critical attributes (same as Scenario A)
        validation_results = {}
        
        # Check mole_fractions
        if hasattr(gas_obj, 'mole_fractions') and gas_obj.mole_fractions is not None:
            validation_results['mole_fractions'] = {
                'exists': True,
                'shape': gas_obj.mole_fractions.shape,
                'index_type': type(gas_obj.mole_fractions.index),
                'index_values': list(gas_obj.mole_fractions.index)
            }
            print(f"   ✅ mole_fractions: {gas_obj.mole_fractions.shape}")
        else:
            validation_results['mole_fractions'] = {'exists': False}
            print("   ❌ mole_fractions: 缺失")
        
        # Check mole_fractions_max
        if hasattr(gas_obj, 'mole_fractions_max') and gas_obj.mole_fractions_max is not None:
            validation_results['mole_fractions_max'] = {
                'exists': True,
                'shape': gas_obj.mole_fractions_max.shape,
                'index_values': list(gas_obj.mole_fractions_max.index)
            }
            print(f"   ✅ mole_fractions_max: {gas_obj.mole_fractions_max.shape}")
        else:
            validation_results['mole_fractions_max'] = {'exists': False}
            print("   ❌ mole_fractions_max: 缺失")
        
        # Check possible_reactants
        if hasattr(gas_obj, 'possible_reactants') and gas_obj.possible_reactants is not None:
            validation_results['possible_reactants'] = {
                'exists': True,
                'count': len(gas_obj.possible_reactants),
                'values': gas_obj.possible_reactants
            }
            print(f"   ✅ possible_reactants: {len(gas_obj.possible_reactants)} 个")
        else:
            validation_results['possible_reactants'] = {'exists': False}
            print("   ❌ possible_reactants: 缺失")
        
        print("✅ Scenario B 测试完成")
        return True, validation_results
        
    except Exception as e:
        print(f"❌ Scenario B 测试失败: {str(e)}")
        return False, {}

def compare_scenarios(results_a, results_b):
    """Compare results between scenarios to ensure consistency"""
    print("=" * 80)
    print("比较两个场景的数据结构一致性")
    print("=" * 80)
    
    if not results_a or not results_b:
        print("❌ 无法比较：一个或两个场景测试失败")
        return False
    
    consistency_check = True
    
    for attr in ['mole_fractions', 'mole_fractions_max', 'possible_reactants']:
        if attr in results_a and attr in results_b:
            exists_a = results_a[attr].get('exists', False)
            exists_b = results_b[attr].get('exists', False)
            
            if exists_a and exists_b:
                print(f"   ✅ {attr}: 两个场景都存在")
                
                # Check shape consistency for DataFrames
                if 'shape' in results_a[attr] and 'shape' in results_b[attr]:
                    shape_a = results_a[attr]['shape']
                    shape_b = results_b[attr]['shape']
                    if shape_a[1] == shape_b[1]:  # Same number of columns (species)
                        print(f"      ✅ 列数一致: {shape_a[1]}")
                    else:
                        print(f"      ❌ 列数不一致: {shape_a[1]} vs {shape_b[1]}")
                        consistency_check = False
                        
            elif exists_a != exists_b:
                print(f"   ❌ {attr}: 存在性不一致 (A: {exists_a}, B: {exists_b})")
                consistency_check = False
            else:
                print(f"   ⚠️  {attr}: 两个场景都缺失")
    
    if consistency_check:
        print("\n✅ 数据结构一致性检查通过")
    else:
        print("\n❌ 数据结构一致性检查失败")
    
    return consistency_check

def main():
    """Main validation function"""
    print("ChemkinSolution 优化版本验证测试")
    print("=" * 80)
    
    # Test both scenarios
    success_a, results_a = test_scenario_a()
    success_b, results_b = test_scenario_b()
    
    # Compare scenarios
    if success_a and success_b:
        consistency = compare_scenarios(results_a, results_b)
    else:
        consistency = False
    
    # Final summary
    print("=" * 80)
    print("测试总结")
    print("=" * 80)
    print(f"Scenario A (with end_point_sheets): {'✅ 通过' if success_a else '❌ 失败'}")
    print(f"Scenario B (without end_point_sheets): {'✅ 通过' if success_b else '❌ 失败'}")
    print(f"数据结构一致性: {'✅ 通过' if consistency else '❌ 失败'}")
    
    overall_success = success_a and success_b and consistency
    print(f"\n总体结果: {'✅ 所有测试通过' if overall_success else '❌ 存在失败的测试'}")
    
    if not overall_success:
        print("\n⚠️  需要应用 chemkin_solution_fixes.py 中的修复代码")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
